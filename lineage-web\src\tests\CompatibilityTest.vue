<template>
  <div class="compatibility-test">
    <div class="test-header">
      <h1>🌐 兼容性测试</h1>
      <p>检测浏览器兼容性、设备支持和功能可用性</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-space>
        <a-button
          type="primary"
          size="large"
          :loading="isRunningTest"
          @click="runCompatibilityTest"
        >
          <template #icon><CheckCircleOutlined /></template>
          运行兼容性测试
        </a-button>

        <a-button @click="exportReport">导出报告</a-button>
        <a-button @click="clearResults">清空结果</a-button>
      </a-space>
    </div>

    <!-- 测试结果展示 -->
    <div v-if="testResult" class="test-results">
      <!-- 总体评分 -->
      <a-card title="兼容性评分" class="score-card" :bordered="false">
        <div class="score-display">
          <a-progress
            type="circle"
            :percent="testResult.score"
            :status="testResult.passed ? 'success' : 'exception'"
            :width="120"
          />
          <div class="score-info">
            <h2>{{ testResult.score }}/100</h2>
            <p>{{ testResult.passed ? '兼容性良好' : '存在兼容性问题' }}</p>
          </div>
        </div>
      </a-card>

      <!-- 详细信息 -->
      <a-row :gutter="16" class="detail-cards">
        <!-- 浏览器信息 -->
        <a-col :span="8">
          <a-card title="浏览器信息" :bordered="false">
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="浏览器">
                <a-tag :color="getBrowserColor(testResult.browser.name)">
                  {{ testResult.browser.name }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="版本">{{ testResult.browser.version }}</a-descriptions-item>
              <a-descriptions-item label="引擎">{{ testResult.browser.engine }}</a-descriptions-item>
              <a-descriptions-item label="平台">{{ testResult.browser.platform }}</a-descriptions-item>
              <a-descriptions-item label="移动设备">
                <a-tag :color="testResult.browser.mobile ? 'blue' : 'green'">
                  {{ testResult.browser.mobile ? '是' : '否' }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- 设备信息 -->
        <a-col :span="8">
          <a-card title="设备信息" :bordered="false">
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="设备类型">
                <a-tag :color="getDeviceColor(testResult.device.type)">
                  {{ getDeviceLabel(testResult.device.type) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="屏幕尺寸">
                {{ testResult.device.screenWidth }} × {{ testResult.device.screenHeight }}
              </a-descriptions-item>
              <a-descriptions-item label="像素比">{{ testResult.device.devicePixelRatio }}</a-descriptions-item>
              <a-descriptions-item label="触摸支持">
                <a-tag :color="testResult.device.touchSupport ? 'green' : 'red'">
                  {{ testResult.device.touchSupport ? '支持' : '不支持' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="屏幕方向">
                <a-tag :color="testResult.device.orientation === 'landscape' ? 'blue' : 'orange'">
                  {{ testResult.device.orientation === 'landscape' ? '横屏' : '竖屏' }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- 功能支持 -->
        <a-col :span="8">
          <a-card title="功能支持" :bordered="false">
            <div class="feature-grid">
              <div
                v-for="(value, key) in testResult.features"
                :key="key"
                class="feature-item"
              >
                <span class="feature-name">{{ getFeatureLabel(key) }}</span>
                <a-tag :color="value ? 'green' : 'red'" size="small">
                  {{ value ? '支持' : '不支持' }}
                </a-tag>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 问题和建议 -->
      <a-row :gutter="16" class="issues-recommendations">
        <a-col :span="12">
          <a-card title="兼容性问题" :bordered="false">
            <div v-if="testResult.issues.length === 0" class="empty-state">
              <a-result status="success" title="无兼容性问题" sub-title="当前环境完全兼容" />
            </div>
            <div v-else>
              <a-alert
                v-for="(issue, index) in testResult.issues"
                :key="index"
                :message="issue"
                type="error"
                show-icon
                class="issue-alert"
              />
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="优化建议" :bordered="false">
            <div v-if="testResult.recommendations.length === 0" class="empty-state">
              <a-result status="info" title="无优化建议" sub-title="当前环境已是最佳配置" />
            </div>
            <div v-else>
              <a-alert
                v-for="(recommendation, index) in testResult.recommendations"
                :key="index"
                :message="recommendation"
                type="info"
                show-icon
                class="recommendation-alert"
              />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 响应式测试 -->
      <a-card title="响应式测试" :bordered="false" class="responsive-test">
        <div class="responsive-controls">
          <a-space>
            <span>模拟屏幕尺寸：</span>
            <a-button
              v-for="size in screenSizes"
              :key="size.name"
              :type="currentScreenSize === size.name ? 'primary' : 'default'"
              size="small"
              @click="setScreenSize(size)"
            >
              {{ size.name }}
            </a-button>
          </a-space>
        </div>

        <div class="responsive-preview" :style="previewStyle">
          <div class="preview-content">
            <h3>血缘图组件预览</h3>
            <p>当前尺寸: {{ currentScreenSize }}</p>
            <div class="mock-lineage-graph">
              <div class="mock-node">表A</div>
              <div class="mock-edge"></div>
              <div class="mock-node">表B</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty description="暂无测试结果，请运行兼容性测试" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import { CheckCircleOutlined } from '@ant-design/icons-vue'
import {
  compatibilityTester,
  type CompatibilityTestResult
} from '@/utils/compatibilityTester'

// 响应式数据
const isRunningTest = ref(false)
const testResult = ref<CompatibilityTestResult | null>(null)
const currentScreenSize = ref('Desktop')

// 屏幕尺寸配置
const screenSizes = [
  { name: 'Mobile', width: 375, height: 667 },
  { name: 'Tablet', width: 768, height: 1024 },
  { name: 'Desktop', width: 1200, height: 800 },
  { name: 'Large', width: 1920, height: 1080 }
]

// 预览样式
const previewStyle = computed(() => {
  const size = screenSizes.find(s => s.name === currentScreenSize.value)
  if (!size) return {}

  return {
    width: `${Math.min(size.width, 800)}px`,
    height: `${Math.min(size.height, 400)}px`,
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    overflow: 'hidden',
    margin: '16px auto',
    transform: size.width > 800 ? `scale(${800 / size.width})` : 'none',
    transformOrigin: 'top center'
  }
})

// 运行兼容性测试
const runCompatibilityTest = async () => {
  isRunningTest.value = true

  try {
    message.info('开始兼容性测试...')

    // 模拟异步测试过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    const result = compatibilityTester.runCompatibilityTest()
    testResult.value = result

    if (result.passed) {
      message.success(`兼容性测试通过！评分: ${result.score}/100`)
    } else {
      message.warning(`兼容性测试发现问题，评分: ${result.score}/100`)
    }
  } catch (error) {
    console.error('兼容性测试失败:', error)
    message.error('兼容性测试失败')
  } finally {
    isRunningTest.value = false
  }
}

// 设置屏幕尺寸
const setScreenSize = (size: { name: string; width: number; height: number }) => {
  currentScreenSize.value = size.name
}

// 获取浏览器颜色
const getBrowserColor = (browserName: string): string => {
  switch (browserName) {
    case 'Chrome': return 'blue'
    case 'Firefox': return 'orange'
    case 'Safari': return 'cyan'
    case 'Edge': return 'green'
    case 'Opera': return 'red'
    default: return 'default'
  }
}

// 获取设备颜色
const getDeviceColor = (deviceType: string): string => {
  switch (deviceType) {
    case 'mobile': return 'blue'
    case 'tablet': return 'orange'
    case 'desktop': return 'green'
    default: return 'default'
  }
}

// 获取设备标签
const getDeviceLabel = (deviceType: string): string => {
  switch (deviceType) {
    case 'mobile': return '移动设备'
    case 'tablet': return '平板设备'
    case 'desktop': return '桌面设备'
    default: return '未知设备'
  }
}

// 获取功能标签
const getFeatureLabel = (featureKey: string): string => {
  const labels: { [key: string]: string } = {
    canvas: 'Canvas',
    webgl: 'WebGL',
    svg: 'SVG',
    css3: 'CSS3',
    es6: 'ES6',
    modules: 'ES模块',
    webWorkers: 'Web Workers',
    localStorage: '本地存储',
    sessionStorage: '会话存储',
    indexedDB: 'IndexedDB',
    webAssembly: 'WebAssembly',
    intersectionObserver: 'Intersection Observer',
    resizeObserver: 'Resize Observer',
    mutationObserver: 'Mutation Observer'
  }
  return labels[featureKey] || featureKey
}

// 导出报告
const exportReport = () => {
  if (!testResult.value) {
    message.warning('请先运行兼容性测试')
    return
  }

  const report = {
    timestamp: new Date().toISOString(),
    testResult: testResult.value,
    environment: {
      url: window.location.href,
      referrer: document.referrer,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    }
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `compatibility-report-${new Date().toISOString().slice(0, 19)}.json`
  a.click()
  URL.revokeObjectURL(url)

  message.success('兼容性报告已导出')
}

// 清空结果
const clearResults = () => {
  testResult.value = null
  message.info('测试结果已清空')
}
</script>

<style scoped>
.compatibility-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 28px;
}

.test-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.test-controls {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.score-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.score-card :deep(.ant-card-head-title) {
  color: white;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 24px;
}

.score-info h2 {
  margin: 0;
  color: white;
  font-size: 32px;
}

.score-info p {
  margin: 8px 0 0 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.detail-cards {
  margin-bottom: 24px;
}

.feature-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.feature-name {
  font-size: 12px;
  color: #666;
}

.issues-recommendations {
  margin-bottom: 24px;
}

.issue-alert {
  margin-bottom: 8px;
}

.recommendation-alert {
  margin-bottom: 8px;
}

.responsive-test {
  margin-bottom: 24px;
}

.responsive-controls {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.responsive-preview {
  background: #fff;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.preview-content {
  padding: 16px;
  text-align: center;
}

.mock-lineage-graph {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
  padding: 16px;
  background: #f0f2f5;
  border-radius: 4px;
}

.mock-node {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.mock-edge {
  width: 40px;
  height: 2px;
  background: #1890ff;
  position: relative;
}

.mock-edge::after {
  content: '';
  position: absolute;
  right: -4px;
  top: -2px;
  width: 0;
  height: 0;
  border-left: 6px solid #1890ff;
  border-top: 3px solid transparent;
  border-bottom: 3px solid transparent;
}

.empty-state {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .compatibility-test {
    padding: 16px;
  }

  .test-header {
    padding: 16px;
  }

  .test-header h1 {
    font-size: 24px;
  }

  .score-display {
    flex-direction: column;
    text-align: center;
  }

  .detail-cards .ant-col {
    margin-bottom: 16px;
  }

  .issues-recommendations .ant-col {
    margin-bottom: 16px;
  }
}

/* 动画效果 */
.ant-card {
  transition: all 0.3s ease;
}

.ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 进度条样式 */
.ant-progress-circle .ant-progress-text {
  color: white !important;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 描述列表样式 */
.ant-descriptions-item-label {
  font-weight: 500;
}
</style>
