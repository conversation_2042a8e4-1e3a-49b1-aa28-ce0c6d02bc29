# LineageLayout 布局溢出问题修复报告

## 问题分析

通过检查 `LineageLayout.vue` 组件的布局实现，发现了以下可能导致右侧面板溢出的问题：

### 1. JavaScript 与 CSS 宽度控制冲突

**问题描述：**
- CSS 中定义了 `--left-panel-width: 40%` 和 `--right-panel-width: 60%`
- JavaScript 中的 `handleResize` 函数动态计算并设置 `leftPanelWidth.value`
- Ant Design 的 `a-layout-sider` 组件同时受到 CSS 和 JavaScript 宽度控制

**根本原因：**
```javascript
// 问题代码
leftPanelWidth.value = Math.floor(window.innerWidth * 0.4)
```
这导致 JavaScript 计算的像素值与 CSS 的百分比值产生冲突。

### 2. 缺少 overflow 控制

**问题描述：**
- 右侧面板及其子元素缺少 `overflow: hidden` 属性
- 当内容超出容器边界时，会产生溢出

### 3. Flex 布局配置不完善

**问题描述：**
- 头部控制区域缺少弹性收缩配置
- 在空间不足时无法正确处理内容溢出

## 修复方案

### 1. 移除 JavaScript 宽度干预

**修复前：**
```javascript
const handleResize = () => {
  if (window.innerWidth < 768) {
    leftPanelWidth.value = 300
  } else {
    leftPanelWidth.value = Math.floor(window.innerWidth * 0.4)
  }
}
```

**修复后：**
```javascript
const handleResize = () => {
  // 宽度完全由CSS控制，避免JavaScript干预
  // 移除动态宽度计算，避免与CSS flex布局冲突
}
```

**修复前：**
```vue
<a-layout-sider :width="leftPanelWidth">
```

**修复后：**
```vue
<a-layout-sider :width="null">
```

### 2. 添加 overflow 控制

**右侧布局容器：**
```css
.right-layout {
  /* 其他样式... */
  overflow: hidden; /* 防止内容溢出 */
}
```

**头部控制栏：**
```css
.right-header {
  /* 其他样式... */
  overflow: hidden; /* 防止头部内容溢出 */
}
```

**头部内容区域：**
```css
.header-content {
  /* 其他样式... */
  width: 100%;
  max-width: 100%;
  overflow: hidden; /* 防止内容溢出 */
}
```

**图谱内容区域：**
```css
.graph-content {
  /* 其他样式... */
  overflow: hidden; /* 防止内容溢出 */
}
```

### 3. 优化 Flex 布局

**头部右侧控制区域：**
```css
.header-right {
  flex-shrink: 1; /* 允许在空间不足时收缩 */
  min-width: 0; /* 允许收缩到最小宽度 */
  overflow: hidden; /* 防止内容溢出 */
}
```

## 布局测试工具

为了验证修复效果，创建了专门的布局测试工具 `src/tests/layoutTest.ts`：

### 主要功能

1. **比例验证：** 检查左右面板是否严格按照 40:60 比例分布
2. **溢出检测：** 扫描右侧面板内的所有元素，检测是否有溢出边界的情况
3. **响应式测试：** 验证在不同屏幕尺寸下的布局行为

### 使用方法

```javascript
// 在浏览器控制台中运行
window.testLayout()           // 运行完整测试套件
window.testLayoutProportions() // 仅测试布局比例
```

### 测试指标

- **宽度比例精度：** 允许 1% 的误差范围
- **溢出检测：** 检查元素是否超出容器边界（允许 1px 误差）
- **响应式行为：** 验证小屏幕时是否切换为垂直布局

## CSS 变量系统

确保 CSS 变量正确定义（在 `src/assets/base.css` 中）：

```css
:root {
  /* 布局比例 */
  --left-panel-width: 40%;
  --right-panel-width: 60%;
  --panel-min-width: 300px;
}
```

## 预期效果

修复后的布局应该具备以下特性：

1. **严格比例：** 左侧面板占据 40% 宽度，右侧面板占据 60% 宽度
2. **无溢出：** 右侧面板内容不会超出可视区域边界
3. **响应式：** 在小屏幕设备上正确切换为垂直布局
4. **一致性：** 在不同浏览器和屏幕尺寸下保持一致的布局表现

## 验证步骤

1. 启动开发服务器
2. 打开浏览器开发者工具
3. 在控制台运行 `window.testLayout()`
4. 检查测试结果，确保所有测试通过
5. 手动调整浏览器窗口大小，观察布局响应
6. 检查右侧面板内容是否有溢出现象

## 注意事项

1. **避免混合控制：** 不要同时使用 JavaScript 和 CSS 控制同一个元素的宽度
2. **Flex 布局优先：** 优先使用 CSS Flex 布局而不是固定像素值
3. **overflow 策略：** 合理使用 `overflow: hidden` 防止内容溢出
4. **测试覆盖：** 在不同设备和浏览器上测试布局表现
