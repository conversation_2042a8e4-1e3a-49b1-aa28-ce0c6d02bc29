<template>
  <div class="detailed-performance-test">
    <div class="test-header">
      <h1>🚀 详细性能测试</h1>
      <p>对血缘图组件进行全面的性能测试和分析</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试配置" :bordered="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="数据量测试">
              <a-space direction="vertical" style="width: 100%">
                <a-button
                  type="primary"
                  :loading="isRunningDataSizeTest"
                  @click="runDataSizeTest"
                  block
                >
                  运行数据量测试
                </a-button>
                <a-select
                  v-model:value="selectedDataSizes"
                  mode="multiple"
                  placeholder="选择测试数据量"
                  style="width: 100%"
                >
                  <a-select-option v-for="size in dataSizeOptions" :key="size" :value="size">
                    {{ size }} 节点
                  </a-select-option>
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="压力测试">
              <a-space direction="vertical" style="width: 100%">
                <a-button
                  type="primary"
                  :loading="isRunningStressTest"
                  @click="runStressTest"
                  block
                >
                  运行压力测试
                </a-button>
                <a-input-number
                  v-model:value="stressTestDuration"
                  :min="5000"
                  :max="60000"
                  :step="5000"
                  placeholder="测试时长(ms)"
                  style="width: 100%"
                />
              </a-space>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="基准测试">
              <a-space direction="vertical" style="width: 100%">
                <a-button
                  type="primary"
                  :loading="isRunningBenchmark"
                  @click="runBenchmarkTest"
                  block
                >
                  运行基准测试
                </a-button>
                <a-button @click="exportResults" block>
                  导出测试报告
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 测试结果展示 -->
    <div class="test-results">
      <a-tabs v-model:activeKey="activeTab">
        <!-- 数据量测试结果 -->
        <a-tab-pane key="dataSize" tab="数据量测试">
          <a-card title="数据量性能测试结果" :bordered="false">
            <div v-if="dataSizeResults.length === 0" class="empty-state">
              <a-empty description="暂无测试结果，请运行数据量测试" />
            </div>
            <div v-else>
              <!-- 性能图表 -->
              <div class="performance-charts">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <div class="chart-container">
                      <h3>渲染时间 vs 数据量</h3>
                      <div class="chart-placeholder">
                        <canvas ref="renderTimeChart" width="400" height="200"></canvas>
                      </div>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div class="chart-container">
                      <h3>内存使用 vs 数据量</h3>
                      <div class="chart-placeholder">
                        <canvas ref="memoryChart" width="400" height="200"></canvas>
                      </div>
                    </div>
                  </a-col>
                </a-row>
              </div>

              <!-- 详细结果表格 -->
              <a-table
                :columns="dataSizeColumns"
                :data-source="dataSizeResults"
                :pagination="false"
                size="small"
                class="results-table"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-tag :color="record.success ? 'green' : 'red'">
                      {{ record.success ? '通过' : '失败' }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'performance'">
                    <a-progress
                      :percent="getPerformanceScore(record)"
                      :status="getPerformanceStatus(record)"
                      size="small"
                    />
                  </template>
                </template>
              </a-table>
            </div>
          </a-card>
        </a-tab-pane>

        <!-- 压力测试结果 -->
        <a-tab-pane key="stress" tab="压力测试">
          <a-card title="压力测试结果" :bordered="false">
            <div v-if="!stressTestResult" class="empty-state">
              <a-empty description="暂无测试结果，请运行压力测试" />
            </div>
            <div v-else>
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic
                    title="平均渲染时间"
                    :value="stressTestResult.metrics.renderTime"
                    suffix="ms"
                    :value-style="{ color: stressTestResult.metrics.renderTime < 100 ? '#3f8600' : '#cf1322' }"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="内存增长"
                    :value="stressTestResult.metrics.memoryUsage"
                    suffix="MB"
                    :value-style="{ color: stressTestResult.metrics.memoryUsage < 50 ? '#3f8600' : '#cf1322' }"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="平均FPS"
                    :value="stressTestResult.metrics.fps"
                    :value-style="{ color: stressTestResult.metrics.fps > 30 ? '#3f8600' : '#cf1322' }"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="CPU使用率"
                    :value="stressTestResult.metrics.cpuUsage"
                    suffix="%"
                    :value-style="{ color: stressTestResult.metrics.cpuUsage < 70 ? '#3f8600' : '#cf1322' }"
                  />
                </a-col>
              </a-row>

              <a-divider />

              <div class="stress-test-details">
                <h3>测试详情</h3>
                <a-descriptions :column="2" bordered>
                  <a-descriptions-item label="测试名称">{{ stressTestResult.testName }}</a-descriptions-item>
                  <a-descriptions-item label="测试状态">
                    <a-tag :color="stressTestResult.success ? 'green' : 'red'">
                      {{ stressTestResult.success ? '通过' : '失败' }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="处理节点数">{{ stressTestResult.dataSize.nodeCount }}</a-descriptions-item>
                  <a-descriptions-item label="处理边数">{{ stressTestResult.dataSize.edgeCount }}</a-descriptions-item>
                  <a-descriptions-item label="布局时间">{{ stressTestResult.metrics.layoutTime }}ms</a-descriptions-item>
                  <a-descriptions-item label="错误信息" v-if="stressTestResult.error">
                    <span style="color: red">{{ stressTestResult.error }}</span>
                  </a-descriptions-item>
                </a-descriptions>
              </div>
            </div>
          </a-card>
        </a-tab-pane>

        <!-- 基准测试结果 -->
        <a-tab-pane key="benchmark" tab="基准测试">
          <a-card title="基准测试结果" :bordered="false">
            <div v-if="benchmarkResults.length === 0" class="empty-state">
              <a-empty description="暂无测试结果，请运行基准测试" />
            </div>
            <div v-else>
              <div class="benchmark-summary">
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-card size="small">
                      <a-statistic
                        title="小数据量 (≤50节点)"
                        :value="getBenchmarkPassRate('small')"
                        suffix="% 通过"
                        :value-style="{ color: getBenchmarkPassRate('small') >= 90 ? '#3f8600' : '#cf1322' }"
                      />
                    </a-card>
                  </a-col>
                  <a-col :span="8">
                    <a-card size="small">
                      <a-statistic
                        title="中等数据量 (50-200节点)"
                        :value="getBenchmarkPassRate('medium')"
                        suffix="% 通过"
                        :value-style="{ color: getBenchmarkPassRate('medium') >= 80 ? '#3f8600' : '#cf1322' }"
                      />
                    </a-card>
                  </a-col>
                  <a-col :span="8">
                    <a-card size="small">
                      <a-statistic
                        title="大数据量 (≥200节点)"
                        :value="getBenchmarkPassRate('large')"
                        suffix="% 通过"
                        :value-style="{ color: getBenchmarkPassRate('large') >= 70 ? '#3f8600' : '#cf1322' }"
                      />
                    </a-card>
                  </a-col>
                </a-row>
              </div>

              <a-divider />

              <a-table
                :columns="benchmarkColumns"
                :data-source="benchmarkResults"
                :pagination="false"
                size="small"
                class="results-table"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'category'">
                    <a-tag :color="getCategoryColor(record.evaluation.category)">
                      {{ getCategoryLabel(record.evaluation.category) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'status'">
                    <a-tag :color="record.evaluation.passed ? 'green' : 'red'">
                      {{ record.evaluation.passed ? '通过' : '失败' }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'issues'">
                    <div v-if="record.evaluation.issues.length > 0">
                      <a-tag v-for="issue in record.evaluation.issues" :key="issue" color="red" size="small">
                        {{ issue }}
                      </a-tag>
                    </div>
                    <span v-else style="color: #52c41a">无问题</span>
                  </template>
                </template>
              </a-table>
            </div>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  performanceTester,
  type PerformanceTestResult,
  DEFAULT_BENCHMARKS
} from '@/utils/performanceTester'

// 响应式数据
const activeTab = ref('dataSize')
const isRunningDataSizeTest = ref(false)
const isRunningStressTest = ref(false)
const isRunningBenchmark = ref(false)

// 测试配置
const selectedDataSizes = ref([10, 25, 50, 100, 200, 500])
const dataSizeOptions = [10, 25, 50, 100, 200, 500, 1000, 2000]
const stressTestDuration = ref(10000)

// 测试结果
const dataSizeResults = ref<PerformanceTestResult[]>([])
const stressTestResult = ref<PerformanceTestResult | null>(null)
const benchmarkResults = ref<Array<PerformanceTestResult & { evaluation: any }>>([])

// 图表引用
const renderTimeChart = ref<HTMLCanvasElement>()
const memoryChart = ref<HTMLCanvasElement>()

// 表格列定义
const dataSizeColumns = [
  { title: '测试名称', dataIndex: 'testName', key: 'testName' },
  { title: '节点数', dataIndex: ['dataSize', 'nodeCount'], key: 'nodeCount' },
  { title: '渲染时间(ms)', dataIndex: ['metrics', 'renderTime'], key: 'renderTime' },
  { title: '内存使用(MB)', dataIndex: ['metrics', 'memoryUsage'], key: 'memoryUsage' },
  { title: 'FPS', dataIndex: ['metrics', 'fps'], key: 'fps' },
  { title: '状态', key: 'status' },
  { title: '性能评分', key: 'performance' }
]

const benchmarkColumns = [
  { title: '测试名称', dataIndex: 'testName', key: 'testName' },
  { title: '数据类别', key: 'category' },
  { title: '节点数', dataIndex: ['dataSize', 'nodeCount'], key: 'nodeCount' },
  { title: '渲染时间(ms)', dataIndex: ['metrics', 'renderTime'], key: 'renderTime' },
  { title: '内存使用(MB)', dataIndex: ['metrics', 'memoryUsage'], key: 'memoryUsage' },
  { title: 'FPS', dataIndex: ['metrics', 'fps'], key: 'fps' },
  { title: '状态', key: 'status' },
  { title: '问题', key: 'issues' }
]

// 运行数据量测试
const runDataSizeTest = async () => {
  if (selectedDataSizes.value.length === 0) {
    message.warning('请选择至少一个数据量进行测试')
    return
  }

  isRunningDataSizeTest.value = true
  dataSizeResults.value = []

  try {
    message.info('开始数据量性能测试...')
    const results = await performanceTester.runDataSizeTest(selectedDataSizes.value)
    dataSizeResults.value = results

    // 绘制图表
    await nextTick()
    drawCharts()

    const passedCount = results.filter(r => r.success).length
    message.success(`数据量测试完成！通过 ${passedCount}/${results.length} 项测试`)
  } catch (error) {
    console.error('数据量测试失败:', error)
    message.error('数据量测试失败')
  } finally {
    isRunningDataSizeTest.value = false
  }
}

// 运行压力测试
const runStressTest = async () => {
  isRunningStressTest.value = true
  stressTestResult.value = null

  try {
    message.info(`开始压力测试 (${stressTestDuration.value}ms)...`)
    const result = await performanceTester.runStressTest(stressTestDuration.value)
    stressTestResult.value = result

    if (result.success) {
      message.success('压力测试通过！')
    } else {
      message.warning('压力测试未达到预期性能标准')
    }
  } catch (error) {
    console.error('压力测试失败:', error)
    message.error('压力测试失败')
  } finally {
    isRunningStressTest.value = false
  }
}

// 运行基准测试
const runBenchmarkTest = async () => {
  isRunningBenchmark.value = true
  benchmarkResults.value = []

  try {
    message.info('开始基准测试...')

    // 运行不同数据量的基准测试
    const testSizes = [25, 100, 300] // 小、中、大数据量
    const results = await performanceTester.runDataSizeTest(testSizes)

    // 评估每个结果
    const evaluatedResults = results.map(result => ({
      ...result,
      evaluation: performanceTester.evaluatePerformance(result, DEFAULT_BENCHMARKS)
    }))

    benchmarkResults.value = evaluatedResults

    const passedCount = evaluatedResults.filter(r => r.evaluation.passed).length
    message.success(`基准测试完成！通过 ${passedCount}/${evaluatedResults.length} 项测试`)
  } catch (error) {
    console.error('基准测试失败:', error)
    message.error('基准测试失败')
  } finally {
    isRunningBenchmark.value = false
  }
}

// 绘制性能图表
const drawCharts = () => {
  if (!renderTimeChart.value || !memoryChart.value) return

  // 绘制渲染时间图表
  drawLineChart(
    renderTimeChart.value,
    dataSizeResults.value.map(r => r.dataSize.nodeCount),
    dataSizeResults.value.map(r => r.metrics.renderTime),
    '渲染时间 (ms)',
    '#1890ff'
  )

  // 绘制内存使用图表
  drawLineChart(
    memoryChart.value,
    dataSizeResults.value.map(r => r.dataSize.nodeCount),
    dataSizeResults.value.map(r => r.metrics.memoryUsage),
    '内存使用 (MB)',
    '#52c41a'
  )
}

// 简单的线图绘制函数
const drawLineChart = (canvas: HTMLCanvasElement, xData: number[], yData: number[], label: string, color: string) => {
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const width = canvas.width
  const height = canvas.height
  const padding = 40

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 计算数据范围
  const xMin = Math.min(...xData)
  const xMax = Math.max(...xData)
  const yMin = 0
  const yMax = Math.max(...yData) * 1.1

  // 绘制坐标轴
  ctx.strokeStyle = '#d9d9d9'
  ctx.lineWidth = 1
  ctx.beginPath()
  ctx.moveTo(padding, padding)
  ctx.lineTo(padding, height - padding)
  ctx.lineTo(width - padding, height - padding)
  ctx.stroke()

  // 绘制数据线
  ctx.strokeStyle = color
  ctx.lineWidth = 2
  ctx.beginPath()

  for (let i = 0; i < xData.length; i++) {
    const x = padding + ((xData[i] - xMin) / (xMax - xMin)) * (width - 2 * padding)
    const y = height - padding - ((yData[i] - yMin) / (yMax - yMin)) * (height - 2 * padding)

    if (i === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }

    // 绘制数据点
    ctx.fillStyle = color
    ctx.beginPath()
    ctx.arc(x, y, 3, 0, 2 * Math.PI)
    ctx.fill()
  }

  ctx.stroke()

  // 绘制标签
  ctx.fillStyle = '#666'
  ctx.font = '12px Arial'
  ctx.fillText('节点数', width - 60, height - 10)
  ctx.save()
  ctx.translate(15, height / 2)
  ctx.rotate(-Math.PI / 2)
  ctx.fillText(label, 0, 0)
  ctx.restore()
}

// 计算性能评分
const getPerformanceScore = (result: PerformanceTestResult): number => {
  const { nodeCount } = result.dataSize
  const { renderTime, memoryUsage, fps } = result.metrics

  let score = 100

  // 根据数据量调整基准
  let maxRenderTime = 100
  let maxMemory = 10
  let minFps = 60

  if (nodeCount > 200) {
    maxRenderTime = 2000
    maxMemory = 200
    minFps = 15
  } else if (nodeCount > 50) {
    maxRenderTime = 500
    maxMemory = 50
    minFps = 30
  }

  // 渲染时间评分 (40%)
  if (renderTime > maxRenderTime) {
    score -= 40 * Math.min((renderTime - maxRenderTime) / maxRenderTime, 1)
  }

  // 内存使用评分 (30%)
  if (memoryUsage > maxMemory) {
    score -= 30 * Math.min((memoryUsage - maxMemory) / maxMemory, 1)
  }

  // FPS评分 (30%)
  if (fps < minFps) {
    score -= 30 * Math.min((minFps - fps) / minFps, 1)
  }

  return Math.max(0, Math.round(score))
}

// 获取性能状态
const getPerformanceStatus = (result: PerformanceTestResult): 'success' | 'active' | 'exception' => {
  const score = getPerformanceScore(result)
  if (score >= 80) return 'success'
  if (score >= 60) return 'active'
  return 'exception'
}

// 获取基准测试通过率
const getBenchmarkPassRate = (category: 'small' | 'medium' | 'large'): number => {
  const categoryResults = benchmarkResults.value.filter(r => r.evaluation.category === category)
  if (categoryResults.length === 0) return 0

  const passedCount = categoryResults.filter(r => r.evaluation.passed).length
  return Math.round((passedCount / categoryResults.length) * 100)
}

// 获取类别颜色
const getCategoryColor = (category: 'small' | 'medium' | 'large'): string => {
  switch (category) {
    case 'small': return 'green'
    case 'medium': return 'orange'
    case 'large': return 'red'
    default: return 'default'
  }
}

// 获取类别标签
const getCategoryLabel = (category: 'small' | 'medium' | 'large'): string => {
  switch (category) {
    case 'small': return '小数据量'
    case 'medium': return '中等数据量'
    case 'large': return '大数据量'
    default: return '未知'
  }
}

// 导出测试报告
const exportResults = () => {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      dataSizeTests: dataSizeResults.value.length,
      stressTest: stressTestResult.value ? 1 : 0,
      benchmarkTests: benchmarkResults.value.length
    },
    dataSizeResults: dataSizeResults.value,
    stressTestResult: stressTestResult.value,
    benchmarkResults: benchmarkResults.value,
    benchmarks: DEFAULT_BENCHMARKS
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `detailed-performance-report-${new Date().toISOString().slice(0, 19)}.json`
  a.click()
  URL.revokeObjectURL(url)

  message.success('性能测试报告已导出')
}

// 组件挂载时初始化
onMounted(() => {
  console.log('详细性能测试页面已加载')
})
</script>

<style scoped>
.detailed-performance-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 28px;
}

.test-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.test-controls {
  margin-bottom: 24px;
}

.test-results {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.empty-state {
  padding: 40px;
  text-align: center;
}

.performance-charts {
  margin-bottom: 24px;
}

.chart-container {
  text-align: center;
}

.chart-container h3 {
  margin-bottom: 16px;
  color: #333;
}

.chart-placeholder {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  background: #fafafa;
}

.results-table {
  margin-top: 16px;
}

.stress-test-details {
  margin-top: 24px;
}

.benchmark-summary {
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detailed-performance-test {
    padding: 16px;
  }

  .test-header {
    padding: 16px;
  }

  .test-header h1 {
    font-size: 24px;
  }
}

/* 动画效果 */
.ant-card {
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 统计数字样式 */
.ant-statistic-content-value {
  font-weight: 600;
}

/* 表格样式优化 */
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px 16px;
}

/* 进度条样式 */
.ant-progress-success-bg {
  background-color: #52c41a !important;
}

.ant-progress-exception-bg {
  background-color: #ff4d4f !important;
}
</style>
