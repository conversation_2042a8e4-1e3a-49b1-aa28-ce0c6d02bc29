<template>
  <div class="field-edge-test">
    <h2>字段级连线功能测试</h2>
    
    <div class="test-controls">
      <a-button type="primary" @click="runTests">运行测试</a-button>
      <a-button @click="clearResults">清空结果</a-button>
    </div>
    
    <div class="test-results" v-if="testResults.length > 0">
      <h3>测试结果</h3>
      <div class="result-item" v-for="(result, index) in testResults" :key="index">
        <div class="result-header">
          <span class="result-status" :class="result.success ? 'success' : 'error'">
            {{ result.success ? '✅' : '❌' }}
          </span>
          <span class="result-name">{{ result.name }}</span>
        </div>
        <div class="result-details" v-if="result.details">
          <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data } from '@/utils/graphDataTransform'
import { registerAllGraphElements } from '@/utils/registerFieldNode'

interface TestResult {
  name: string
  success: boolean
  details?: any
  error?: string
}

const testResults = ref<TestResult[]>([])

// 测试1: 图形元素注册
const testGraphElementsRegistration = (): TestResult => {
  try {
    registerAllGraphElements()
    return {
      name: '图形元素注册测试',
      success: true,
      details: '表节点和字段级边注册成功'
    }
  } catch (error) {
    return {
      name: '图形元素注册测试',
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }
  }
}

// 测试2: 数据转换
const testDataTransformation = (): TestResult => {
  try {
    const lineageData = createSampleLineageData()
    const g6Data = transformToG6Data(lineageData)
    
    // 验证转换结果
    const hasNodes = g6Data.nodes.length > 0
    const hasEdges = g6Data.edges.length > 0
    const hasFieldInfo = g6Data.edges.every(edge => 
      edge.sourceField && edge.targetField && edge.transformType
    )
    
    return {
      name: '数据转换测试',
      success: hasNodes && hasEdges && hasFieldInfo,
      details: {
        nodeCount: g6Data.nodes.length,
        edgeCount: g6Data.edges.length,
        sampleEdge: g6Data.edges[0],
        hasFieldInfo
      }
    }
  } catch (error) {
    return {
      name: '数据转换测试',
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }
  }
}

// 测试3: 边样式配置
const testEdgeStyles = (): TestResult => {
  try {
    const lineageData = createSampleLineageData()
    const g6Data = transformToG6Data(lineageData)
    
    // 检查边样式配置
    const edgeStylesValid = g6Data.edges.every(edge => 
      edge.style && 
      edge.style.stroke && 
      edge.style.lineWidth &&
      edge.type === 'field-edge'
    )
    
    return {
      name: '边样式配置测试',
      success: edgeStylesValid,
      details: {
        edgeTypes: [...new Set(g6Data.edges.map(e => e.type))],
        transformTypes: [...new Set(g6Data.edges.map(e => e.transformType))],
        sampleStyles: g6Data.edges.map(e => ({
          id: e.id,
          type: e.type,
          transformType: e.transformType,
          style: e.style
        }))
      }
    }
  } catch (error) {
    return {
      name: '边样式配置测试',
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }
  }
}

// 运行所有测试
const runTests = () => {
  testResults.value = []
  
  console.log('🚀 开始字段级连线功能测试')
  
  // 运行测试
  const results = [
    testGraphElementsRegistration(),
    testDataTransformation(),
    testEdgeStyles()
  ]
  
  testResults.value = results
  
  // 显示测试总结
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  if (successCount === totalCount) {
    message.success(`所有测试通过 (${successCount}/${totalCount})`)
  } else {
    message.warning(`部分测试失败 (${successCount}/${totalCount})`)
  }
  
  console.log('📊 测试结果:', results)
}

// 清空结果
const clearResults = () => {
  testResults.value = []
  message.info('测试结果已清空')
}
</script>

<style scoped>
.field-edge-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-controls {
  margin: 20px 0;
  display: flex;
  gap: 12px;
}

.test-results {
  margin-top: 20px;
}

.result-item {
  margin-bottom: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.result-status.success {
  color: #52c41a;
}

.result-status.error {
  color: #ff4d4f;
}

.result-name {
  font-weight: 500;
  font-size: 14px;
}

.result-details {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.result-details pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
