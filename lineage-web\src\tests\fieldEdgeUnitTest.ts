/**
 * 字段级连线单元测试
 */

import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data, getEdgeColorByTransformType, getEdgeWidthByConfidence, getEdgeDashByTransformType } from '@/utils/graphDataTransform'

/**
 * 测试数据转换功能
 */
export function testDataTransformation() {
  console.log('=== 测试数据转换功能 ===')
  
  // 创建示例数据
  const lineageData = createSampleLineageData()
  console.log('✅ 创建示例数据成功:', {
    tables: Object.keys(lineageData.tables).length,
    nodes: lineageData.nodes.length,
    edges: lineageData.edges.length
  })
  
  // 转换为G6数据
  const g6Data = transformToG6Data(lineageData)
  console.log('✅ 转换G6数据成功:', {
    nodes: g6Data.nodes.length,
    edges: g6Data.edges.length
  })
  
  // 验证边数据结构
  let allEdgesValid = true
  g6Data.edges.forEach((edge, index) => {
    const isValid = edge.id && edge.source && edge.target && edge.type === 'field-edge' && 
                   edge.sourceField && edge.targetField && edge.transformType
    
    if (!isValid) {
      console.error(`❌ 边 ${index + 1} 数据结构无效:`, edge)
      allEdgesValid = false
    } else {
      console.log(`✅ 边 ${index + 1} 数据结构有效:`, {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceField: edge.sourceField,
        targetField: edge.targetField,
        transformType: edge.transformType,
        confidence: edge.confidence
      })
    }
  })
  
  return {
    success: allEdgesValid,
    data: g6Data,
    edgeCount: g6Data.edges.length,
    nodeCount: g6Data.nodes.length
  }
}

/**
 * 测试边样式功能
 */
export function testEdgeStyles() {
  console.log('=== 测试边样式功能 ===')
  
  const transformTypes = ['DIRECT', 'JOIN', 'AGGREGATE', 'FILTER', 'TRANSFORM', 'UNION', 'WINDOW']
  const confidences = [1.0, 0.9, 0.7, 0.5, 0.3]
  
  console.log('测试转换类型颜色:')
  transformTypes.forEach(type => {
    const color = getEdgeColorByTransformType(type)
    console.log(`  ${type}: ${color}`)
  })
  
  console.log('测试置信度线宽:')
  confidences.forEach(confidence => {
    const width = getEdgeWidthByConfidence(confidence)
    console.log(`  ${confidence}: ${width}px`)
  })
  
  console.log('测试转换类型虚线样式:')
  transformTypes.forEach(type => {
    const dash = getEdgeDashByTransformType(type)
    console.log(`  ${type}: ${dash ? dash.join(',') : '实线'}`)
  })
  
  return {
    success: true,
    transformTypes,
    confidences
  }
}

/**
 * 测试字段级连接逻辑
 */
export function testFieldLevelConnection() {
  console.log('=== 测试字段级连接逻辑 ===')
  
  const lineageData = createSampleLineageData()
  const g6Data = transformToG6Data(lineageData)
  
  // 验证字段级连接
  let connectionValid = true
  g6Data.edges.forEach((edge, index) => {
    const sourceTable = edge.source
    const targetTable = edge.target
    const sourceField = edge.sourceField
    const targetField = edge.targetField
    
    // 检查源表是否包含源字段
    const sourceNode = g6Data.nodes.find(n => n.tableName === sourceTable)
    const targetNode = g6Data.nodes.find(n => n.tableName === targetTable)
    
    if (!sourceNode || !targetNode) {
      console.error(`❌ 边 ${index + 1}: 找不到对应的表节点`)
      connectionValid = false
      return
    }
    
    const sourceFieldExists = sourceNode.fields.some(f => f.fieldName === sourceField)
    const targetFieldExists = targetNode.fields.some(f => f.fieldName === targetField)
    
    if (!sourceFieldExists || !targetFieldExists) {
      console.error(`❌ 边 ${index + 1}: 字段不存在`, {
        sourceTable,
        sourceField,
        sourceFieldExists,
        targetTable,
        targetField,
        targetFieldExists
      })
      connectionValid = false
    } else {
      console.log(`✅ 边 ${index + 1}: 字段级连接有效`, {
        connection: `${sourceTable}.${sourceField} -> ${targetTable}.${targetField}`,
        transformType: edge.transformType
      })
    }
  })
  
  return {
    success: connectionValid,
    totalConnections: g6Data.edges.length
  }
}

/**
 * 运行所有字段级连线测试
 */
export function runAllFieldEdgeTests() {
  console.log('🚀 开始字段级连线完整测试')
  console.log('=' .repeat(50))
  
  const results = {
    dataTransformation: testDataTransformation(),
    edgeStyles: testEdgeStyles(),
    fieldLevelConnection: testFieldLevelConnection()
  }
  
  console.log('=' .repeat(50))
  console.log('📊 测试总结:')
  console.log(`数据转换: ${results.dataTransformation.success ? '✅ 通过' : '❌ 失败'}`)
  console.log(`边样式: ${results.edgeStyles.success ? '✅ 通过' : '❌ 失败'}`)
  console.log(`字段级连接: ${results.fieldLevelConnection.success ? '✅ 通过' : '❌ 失败'}`)
  
  const allPassed = Object.values(results).every(r => r.success)
  console.log(`总体结果: ${allPassed ? '🎉 所有测试通过' : '⚠️ 部分测试失败'}`)
  
  return {
    allPassed,
    results
  }
}
