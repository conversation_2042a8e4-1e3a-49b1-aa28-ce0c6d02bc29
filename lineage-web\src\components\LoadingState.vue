<template>
  <div class="loading-state" :class="{ 'loading-state--overlay': overlay }">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-content">
      <div class="loading-spinner">
        <a-spin :size="spinSize" :tip="loadingText">
          <template #indicator>
            <LoadingOutlined v-if="spinnerType === 'default'" />
            <SyncOutlined v-else-if="spinnerType === 'sync'" spin />
            <ReloadOutlined v-else-if="spinnerType === 'reload'" spin />
            <CloudDownloadOutlined v-else-if="spinnerType === 'download'" />
          </template>
        </a-spin>
      </div>

      <!-- 进度条 -->
      <div v-if="showProgress && progress !== undefined" class="loading-progress">
        <a-progress
          :percent="progress"
          :status="progressStatus"
          :show-info="showProgressInfo"
          :stroke-color="progressColor"
        />
        <div v-if="progressText" class="progress-text">{{ progressText }}</div>
      </div>

      <!-- 加载详情 -->
      <div v-if="loadingDetails" class="loading-details">
        <div class="loading-steps">
          <div
            v-for="(step, index) in loadingSteps"
            :key="index"
            class="loading-step"
            :class="{
              'loading-step--active': step.status === 'active',
              'loading-step--completed': step.status === 'completed',
              'loading-step--error': step.status === 'error'
            }"
          >
            <div class="step-icon">
              <CheckCircleOutlined v-if="step.status === 'completed'" />
              <ExclamationCircleOutlined v-else-if="step.status === 'error'" />
              <LoadingOutlined v-else-if="step.status === 'active'" spin />
              <ClockCircleOutlined v-else />
            </div>
            <div class="step-text">{{ step.text }}</div>
          </div>
        </div>
      </div>

      <!-- 取消按钮 -->
      <div v-if="showCancel" class="loading-actions">
        <a-button @click="handleCancel" size="small">
          <StopOutlined />
          取消
        </a-button>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-content">
      <div class="error-icon">
        <ExclamationCircleOutlined />
      </div>
      <div class="error-message">
        <h4>{{ errorTitle }}</h4>
        <p>{{ errorMessage }}</p>
      </div>

      <!-- 错误详情 -->
      <div v-if="errorDetails && showErrorDetails" class="error-details">
        <a-collapse size="small">
          <a-collapse-panel key="1" header="错误详情">
            <pre>{{ errorDetails }}</pre>
          </a-collapse-panel>
        </a-collapse>
      </div>

      <!-- 错误操作 -->
      <div class="error-actions">
        <a-space>
          <a-button type="primary" @click="handleRetry" v-if="showRetry">
            <ReloadOutlined />
            重试
          </a-button>
          <a-button @click="handleReport" v-if="showReport">
            <BugOutlined />
            报告问题
          </a-button>
          <a-button @click="handleDismiss" v-if="showDismiss">
            关闭
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="empty" class="empty-content">
      <div class="empty-icon">
        <InboxOutlined />
      </div>
      <div class="empty-message">
        <h4>{{ emptyTitle }}</h4>
        <p>{{ emptyMessage }}</p>
      </div>

      <!-- 空状态操作 -->
      <div v-if="emptyActions.length > 0" class="empty-actions">
        <a-space>
          <a-button
            v-for="action in emptyActions"
            :key="action.key"
            :type="action.type || 'default'"
            @click="handleEmptyAction(action)"
          >
            <component :is="action.icon" v-if="action.icon" />
            {{ action.text }}
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 成功状态 -->
    <div v-else-if="success" class="success-content">
      <div class="success-icon">
        <CheckCircleOutlined />
      </div>
      <div class="success-message">
        <h4>{{ successTitle }}</h4>
        <p>{{ successMessage }}</p>
      </div>

      <!-- 成功操作 -->
      <div v-if="successActions.length > 0" class="success-actions">
        <a-space>
          <a-button
            v-for="action in successActions"
            :key="action.key"
            :type="action.type || 'default'"
            @click="handleSuccessAction(action)"
          >
            <component :is="action.icon" v-if="action.icon" />
            {{ action.text }}
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 默认插槽内容 -->
    <div v-else class="default-content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  LoadingOutlined,
  SyncOutlined,
  ReloadOutlined,
  CloudDownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  StopOutlined,
  BugOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface LoadingStep {
  text: string
  status: 'pending' | 'active' | 'completed' | 'error'
}

interface ActionButton {
  key: string
  text: string
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text'
  icon?: any
  handler?: () => void
}

// Props
interface Props {
  // 状态控制
  loading?: boolean
  error?: boolean
  empty?: boolean
  success?: boolean
  overlay?: boolean

  // 加载状态
  loadingText?: string
  spinnerType?: 'default' | 'sync' | 'reload' | 'download'
  spinSize?: 'small' | 'default' | 'large'

  // 进度相关
  showProgress?: boolean
  progress?: number
  progressStatus?: 'normal' | 'exception' | 'active' | 'success'
  progressText?: string
  progressColor?: string
  showProgressInfo?: boolean

  // 加载详情
  loadingDetails?: boolean
  loadingSteps?: LoadingStep[]
  showCancel?: boolean

  // 错误状态
  errorTitle?: string
  errorMessage?: string
  errorDetails?: string
  showErrorDetails?: boolean
  showRetry?: boolean
  showReport?: boolean
  showDismiss?: boolean

  // 空状态
  emptyTitle?: string
  emptyMessage?: string
  emptyActions?: ActionButton[]

  // 成功状态
  successTitle?: string
  successMessage?: string
  successActions?: ActionButton[]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: false,
  empty: false,
  success: false,
  overlay: false,

  loadingText: '加载中...',
  spinnerType: 'default',
  spinSize: 'default',

  showProgress: false,
  progressStatus: 'active',
  showProgressInfo: true,

  loadingDetails: false,
  loadingSteps: () => [],
  showCancel: false,

  errorTitle: '加载失败',
  errorMessage: '请稍后重试或联系技术支持',
  showErrorDetails: false,
  showRetry: true,
  showReport: false,
  showDismiss: true,

  emptyTitle: '暂无数据',
  emptyMessage: '当前没有可显示的内容',
  emptyActions: () => [],

  successTitle: '操作成功',
  successMessage: '操作已成功完成',
  successActions: () => []
})

// Emits
interface Emits {
  (e: 'cancel'): void
  (e: 'retry'): void
  (e: 'report', details: any): void
  (e: 'dismiss'): void
  (e: 'empty-action', action: ActionButton): void
  (e: 'success-action', action: ActionButton): void
}

const emit = defineEmits<Emits>()

// 计算属性
const progressColor = computed(() => {
  if (props.progressColor) return props.progressColor

  switch (props.progressStatus) {
    case 'exception': return '#ff4d4f'
    case 'success': return '#52c41a'
    default: return '#1890ff'
  }
})

// 事件处理
const handleCancel = () => {
  emit('cancel')
}

const handleRetry = () => {
  emit('retry')
}

const handleReport = () => {
  emit('report', {
    title: props.errorTitle,
    message: props.errorMessage,
    details: props.errorDetails,
    timestamp: new Date().toISOString()
  })
}

const handleDismiss = () => {
  emit('dismiss')
}

const handleEmptyAction = (action: ActionButton) => {
  if (action.handler) {
    action.handler()
  }
  emit('empty-action', action)
}

const handleSuccessAction = (action: ActionButton) => {
  if (action.handler) {
    action.handler()
  }
  emit('success-action', action)
}
</script>

<style scoped>
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.loading-state--overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

.loading-content,
.error-content,
.empty-content,
.success-content {
  text-align: center;
  max-width: 400px;
  width: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.loading-spinner {
  margin-bottom: 16px;
}

.loading-progress {
  margin-top: 16px;
  margin-bottom: 16px;
}

.progress-text {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.loading-details {
  margin-top: 20px;
  text-align: left;
}

.loading-steps {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.loading-step {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.loading-step:last-child {
  margin-bottom: 0;
}

.step-icon {
  margin-right: 8px;
  font-size: 14px;
}

.loading-step--completed .step-icon {
  color: #52c41a;
}

.loading-step--error .step-icon {
  color: #ff4d4f;
}

.loading-step--active .step-icon {
  color: #1890ff;
}

.step-text {
  font-size: 14px;
}

.loading-actions,
.error-actions,
.empty-actions,
.success-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.error-icon {
  font-size: 48px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.empty-icon {
  font-size: 48px;
  color: #bfbfbf;
  margin-bottom: 16px;
}

.success-icon {
  font-size: 48px;
  color: #52c41a;
  margin-bottom: 16px;
}

.error-message,
.empty-message,
.success-message {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.error-message h4,
.empty-message h4,
.success-message h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  width: 100%;
}

.error-message p,
.empty-message p,
.success-message p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
  text-align: center;
  width: 100%;
}

.error-details {
  margin: 16px 0;
  text-align: left;
}

.error-details pre {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  max-height: 150px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.default-content {
  width: 100%;
  height: 100%;
}

/* 深色主题 */
.dark .loading-state--overlay {
  background: rgba(0, 0, 0, 0.8);
}

.dark .loading-steps {
  background: #262626;
}

.dark .error-details pre {
  background: #262626;
  border-color: #434343;
  color: #bfbfbf;
}

.dark .error-message p,
.dark .empty-message p,
.dark .success-message p {
  color: #bfbfbf;
}

/* 响应式设计 - 确保在不同屏幕尺寸下都能完美居中 */
@media (max-width: 768px) {
  .loading-state {
    padding: 16px;
    min-height: 150px;
  }

  .loading-content,
  .error-content,
  .empty-content,
  .success-content {
    max-width: 300px;
    width: auto;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .loading-state {
    padding: 12px;
    min-height: 120px;
  }

  .loading-content,
  .error-content,
  .empty-content,
  .success-content {
    max-width: 280px;
    width: auto;
    margin: 0 auto;
  }

  .empty-icon,
  .error-icon,
  .success-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }
}

/* 确保在大屏幕上也能保持良好的居中效果 */
@media (min-width: 1200px) {
  .loading-content,
  .error-content,
  .empty-content,
  .success-content {
    max-width: 500px;
  }
}
</style>
