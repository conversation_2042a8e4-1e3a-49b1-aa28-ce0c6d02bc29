<template>
  <a-tooltip
    v-model:open="visible"
    :title="null"
    placement="right"
    :overlay-class-name="'field-tooltip'"
    :get-popup-container="getPopupContainer"
    :mouse-enter-delay="0.3"
    :mouse-leave-delay="0.1"
  >
    <template #title>
      <div class="field-tooltip-content" v-if="fieldData">
        <!-- 字段基本信息 -->
        <div class="field-header">
          <div class="field-name">
            <span class="field-icon">🔗</span>
            <span class="name">{{ fieldData.fieldName }}</span>
            <a-tag v-if="fieldData.isKey" color="gold" size="small">主键</a-tag>
          </div>
          <div class="field-table">
            <span class="table-icon">📋</span>
            <span>{{ fieldData.tableName }}</span>
          </div>
        </div>

        <a-divider style="margin: 8px 0;" />

        <!-- 数据类型信息 -->
        <div class="field-type" v-if="fieldData.dataType">
          <div class="info-row">
            <span class="label">数据类型:</span>
            <a-tag :color="getTypeColor(fieldData.dataType.type)" size="small">
              {{ formatDataType(fieldData.dataType) }}
            </a-tag>
          </div>
          <div class="info-row" v-if="fieldData.dataType.length">
            <span class="label">长度:</span>
            <span class="value">{{ fieldData.dataType.length }}</span>
          </div>
          <div class="info-row" v-if="fieldData.dataType.precision">
            <span class="label">精度:</span>
            <span class="value">{{ fieldData.dataType.precision }}</span>
          </div>
        </div>

        <!-- 字段属性 -->
        <div class="field-attributes">
          <div class="info-row">
            <span class="label">可为空:</span>
            <a-tag :color="fieldData.isNullable ? 'orange' : 'green'" size="small">
              {{ fieldData.isNullable ? '是' : '否' }}
            </a-tag>
          </div>
          <div class="info-row" v-if="fieldData.defaultValue">
            <span class="label">默认值:</span>
            <span class="value">{{ fieldData.defaultValue }}</span>
          </div>
        </div>

        <!-- 字段描述 -->
        <div class="field-description" v-if="fieldData.description">
          <a-divider style="margin: 8px 0;" />
          <div class="info-row">
            <span class="label">描述:</span>
          </div>
          <div class="description-text">
            {{ fieldData.description }}
          </div>
        </div>

        <!-- 血缘关系统计 -->
        <div class="lineage-stats" v-if="lineageStats">
          <a-divider style="margin: 8px 0;" />
          <div class="stats-header">
            <span class="label">血缘关系:</span>
          </div>
          <div class="stats-content">
            <div class="stat-item">
              <span class="stat-label">上游字段:</span>
              <a-tag color="blue" size="small">{{ lineageStats.upstreamCount }}</a-tag>
            </div>
            <div class="stat-item">
              <span class="stat-label">下游字段:</span>
              <a-tag color="green" size="small">{{ lineageStats.downstreamCount }}</a-tag>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="field-actions">
          <a-divider style="margin: 8px 0;" />
          <a-space size="small">
            <a-button type="link" size="small" @click="handleViewDetails">
              查看详情
            </a-button>
            <a-button type="link" size="small" @click="handleTraceLineage">
              追踪血缘
            </a-button>
          </a-space>
        </div>
      </div>
    </template>
    
    <!-- 触发器插槽 -->
    <slot></slot>
  </a-tooltip>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { LineageNode } from '@/types/lineage'
import { getFieldTypeColor, formatDataType } from '@/utils/graphDataTransform'

// Props
interface Props {
  fieldData?: LineageNode | null
  visible?: boolean
  position?: { x: number; y: number }
}

const props = withDefaults(defineProps<Props>(), {
  fieldData: null,
  visible: false,
  position: () => ({ x: 0, y: 0 })
})

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'view-details': [fieldData: LineageNode]
  'trace-lineage': [fieldData: LineageNode]
}>()

// 响应式数据
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 血缘关系统计（模拟数据，实际应该从store或API获取）
const lineageStats = computed(() => {
  if (!props.fieldData) return null
  
  // 这里应该根据fieldData.id查询实际的血缘关系
  return {
    upstreamCount: Math.floor(Math.random() * 5) + 1,
    downstreamCount: Math.floor(Math.random() * 3) + 1
  }
})

// 方法
const getPopupContainer = () => {
  return document.body
}

const getTypeColor = (dataType: string) => {
  return getFieldTypeColor(dataType)
}

const handleViewDetails = () => {
  if (props.fieldData) {
    emit('view-details', props.fieldData)
  }
  visible.value = false
}

const handleTraceLineage = () => {
  if (props.fieldData) {
    emit('trace-lineage', props.fieldData)
  }
  visible.value = false
}

// 监听fieldData变化，自动显示/隐藏tooltip
watch(() => props.fieldData, (newData) => {
  if (newData && !visible.value) {
    visible.value = true
  } else if (!newData && visible.value) {
    visible.value = false
  }
})
</script>

<style>
/* 全局样式，因为tooltip是挂载到body的 */
.field-tooltip .ant-tooltip-inner {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0;
  max-width: 320px;
  min-width: 280px;
}

.field-tooltip .ant-tooltip-arrow {
  border-right-color: #d9d9d9;
}

.field-tooltip .ant-tooltip-arrow::before {
  border-right-color: #fff;
}
</style>

<style scoped>
.field-tooltip-content {
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
}

.field-header {
  margin-bottom: 8px;
}

.field-name {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
}

.field-icon {
  font-size: 12px;
}

.field-table {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #8c8c8c;
}

.table-icon {
  font-size: 10px;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
  min-height: 20px;
}

.label {
  color: #595959;
  font-size: 11px;
  flex-shrink: 0;
  margin-right: 8px;
}

.value {
  color: #262626;
  font-size: 11px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.description-text {
  color: #595959;
  font-size: 11px;
  line-height: 1.5;
  margin-top: 4px;
  padding: 6px 8px;
  background: #fafafa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.stats-header {
  margin-bottom: 6px;
}

.stats-content {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 10px;
  color: #8c8c8c;
}

.field-actions {
  text-align: center;
}

.field-actions .ant-btn-link {
  padding: 0 4px;
  height: auto;
  font-size: 11px;
}
</style>
