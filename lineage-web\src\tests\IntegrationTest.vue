<template>
  <div class="integration-test-container">
    <!-- 测试页面头部 -->
    <div class="test-header">
      <h1>🧪 全功能集成测试</h1>
      <p>综合测试血缘图组件的所有功能模块，确保系统稳定性和正确性</p>

      <!-- 测试进度概览 -->
      <div class="test-progress">
        <a-progress
          :percent="overallProgress"
          :status="overallStatus"
          :stroke-color="progressColor"
        />
        <div class="progress-stats">
          <a-statistic title="通过测试" :value="passedTests" suffix="项" />
          <a-statistic title="失败测试" :value="failedTests" suffix="项" />
          <a-statistic title="总测试数" :value="totalTests" suffix="项" />
        </div>
      </div>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-space wrap>
        <a-button
          type="primary"
          size="large"
          :loading="isRunningAll"
          @click="runAllTests"
        >
          <template #icon><ThunderboltOutlined /></template>
          运行全部测试
        </a-button>

        <a-button @click="runBasicTests">基础功能测试</a-button>
        <a-button @click="runInteractionTests">交互功能测试</a-button>
        <a-button @click="runPerformanceTests">性能测试</a-button>
        <a-button @click="runErrorHandlingTests">错误处理测试</a-button>

        <a-button @click="clearResults" danger>清空结果</a-button>
        <a-button @click="exportResults">导出报告</a-button>
      </a-space>
    </div>

    <!-- 测试结果展示 -->
    <div class="test-results">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 测试概览 -->
        <a-tab-pane key="overview" tab="测试概览">
          <div class="overview-content">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-card title="基础功能测试" :bordered="false">
                  <div class="test-category">
                    <div class="category-stats">
                      <a-statistic
                        title="通过率"
                        :value="basicTestsPassRate"
                        suffix="%"
                        :value-style="{ color: basicTestsPassRate >= 90 ? '#3f8600' : '#cf1322' }"
                      />
                    </div>
                    <a-list size="small" :data-source="basicTestResults">
                      <template #renderItem="{ item }">
                        <a-list-item>
                          <a-list-item-meta>
                            <template #title>
                              <span :class="item.success ? 'test-pass' : 'test-fail'">
                                {{ item.success ? '✅' : '❌' }} {{ item.title }}
                              </span>
                            </template>
                            <template #description>{{ item.description }}</template>
                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </a-card>
              </a-col>

              <a-col :span="8">
                <a-card title="交互功能测试" :bordered="false">
                  <div class="test-category">
                    <div class="category-stats">
                      <a-statistic
                        title="通过率"
                        :value="interactionTestsPassRate"
                        suffix="%"
                        :value-style="{ color: interactionTestsPassRate >= 90 ? '#3f8600' : '#cf1322' }"
                      />
                    </div>
                    <a-list size="small" :data-source="interactionTestResults">
                      <template #renderItem="{ item }">
                        <a-list-item>
                          <a-list-item-meta>
                            <template #title>
                              <span :class="item.success ? 'test-pass' : 'test-fail'">
                                {{ item.success ? '✅' : '❌' }} {{ item.title }}
                              </span>
                            </template>
                            <template #description>{{ item.description }}</template>
                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </a-card>
              </a-col>

              <a-col :span="8">
                <a-card title="性能测试" :bordered="false">
                  <div class="test-category">
                    <div class="category-stats">
                      <a-statistic
                        title="通过率"
                        :value="performanceTestsPassRate"
                        suffix="%"
                        :value-style="{ color: performanceTestsPassRate >= 80 ? '#3f8600' : '#cf1322' }"
                      />
                    </div>
                    <a-list size="small" :data-source="performanceTestResults">
                      <template #renderItem="{ item }">
                        <a-list-item>
                          <a-list-item-meta>
                            <template #title>
                              <span :class="item.success ? 'test-pass' : 'test-fail'">
                                {{ item.success ? '✅' : '❌' }} {{ item.title }}
                              </span>
                            </template>
                            <template #description>{{ item.description }}</template>
                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 详细测试结果 -->
        <a-tab-pane key="details" tab="详细结果">
          <div class="details-content">
            <a-collapse v-model:activeKey="activeCollapseKeys">
              <a-collapse-panel
                v-for="(category, index) in testCategories"
                :key="index"
                :header="category.name"
              >
                <template #extra>
                  <a-tag :color="category.passRate >= 90 ? 'green' : 'red'">
                    {{ category.passRate }}% 通过
                  </a-tag>
                </template>

                <a-table
                  :columns="testResultColumns"
                  :data-source="category.results"
                  :pagination="false"
                  size="small"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'status'">
                      <a-tag :color="record.success ? 'green' : 'red'">
                        {{ record.success ? '通过' : '失败' }}
                      </a-tag>
                    </template>
                    <template v-else-if="column.key === 'duration'">
                      {{ record.duration }}ms
                    </template>
                    <template v-else-if="column.key === 'details'">
                      <a-button
                        type="link"
                        size="small"
                        @click="showTestDetails(record)"
                      >
                        查看详情
                      </a-button>
                    </template>
                  </template>
                </a-table>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </a-tab-pane>

        <!-- 性能监控 -->
        <a-tab-pane key="performance" tab="性能监控">
          <div class="performance-content">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="渲染性能" :bordered="false">
                  <div class="performance-metrics">
                    <a-statistic
                      title="平均渲染时间"
                      :value="performanceMetrics.avgRenderTime"
                      suffix="ms"
                      :value-style="{ color: performanceMetrics.avgRenderTime < 100 ? '#3f8600' : '#cf1322' }"
                    />
                    <a-statistic
                      title="最大渲染时间"
                      :value="performanceMetrics.maxRenderTime"
                      suffix="ms"
                    />
                    <a-statistic
                      title="内存使用"
                      :value="performanceMetrics.memoryUsage"
                      suffix="MB"
                    />
                  </div>
                </a-card>
              </a-col>

              <a-col :span="12">
                <a-card title="交互性能" :bordered="false">
                  <div class="performance-metrics">
                    <a-statistic
                      title="平均响应时间"
                      :value="performanceMetrics.avgResponseTime"
                      suffix="ms"
                      :value-style="{ color: performanceMetrics.avgResponseTime < 50 ? '#3f8600' : '#cf1322' }"
                    />
                    <a-statistic
                      title="FPS"
                      :value="performanceMetrics.fps"
                      :value-style="{ color: performanceMetrics.fps >= 30 ? '#3f8600' : '#cf1322' }"
                    />
                    <a-statistic
                      title="CPU使用率"
                      :value="performanceMetrics.cpuUsage"
                      suffix="%"
                    />
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ThunderboltOutlined } from '@ant-design/icons-vue'
import { useLineageStore } from '@/stores/lineageStore'
import {
  transformToG6Data,
  validateLineageData,
  applyDagreLayout,
  type DagreLayoutOptions
} from '@/utils/graphDataTransform'
import { createSampleLineageData, formatSql } from '@/utils/sqlParser'
import { registerAllGraphElements } from '@/utils/registerFieldNode'
import type { LineageData, G6GraphData } from '@/types/lineage'

// 测试结果接口
interface TestResult {
  title: string
  description: string
  success: boolean
  duration: number
  details?: any
  error?: string
}

interface TestCategory {
  name: string
  results: TestResult[]
  passRate: number
}

// 响应式数据
const activeTab = ref('overview')
const activeCollapseKeys = ref<string[]>([])
const isRunningAll = ref(false)

// 测试结果
const basicTestResults = ref<TestResult[]>([])
const interactionTestResults = ref<TestResult[]>([])
const performanceTestResults = ref<TestResult[]>([])
const errorHandlingTestResults = ref<TestResult[]>([])

// 性能指标
const performanceMetrics = reactive({
  avgRenderTime: 0,
  maxRenderTime: 0,
  memoryUsage: 0,
  avgResponseTime: 0,
  fps: 0,
  cpuUsage: 0
})

// 计算属性
const totalTests = computed(() => {
  return basicTestResults.value.length +
         interactionTestResults.value.length +
         performanceTestResults.value.length +
         errorHandlingTestResults.value.length
})

const passedTests = computed(() => {
  return basicTestResults.value.filter(t => t.success).length +
         interactionTestResults.value.filter(t => t.success).length +
         performanceTestResults.value.filter(t => t.success).length +
         errorHandlingTestResults.value.filter(t => t.success).length
})

const failedTests = computed(() => totalTests.value - passedTests.value)

const overallProgress = computed(() => {
  return totalTests.value > 0 ? Math.round((passedTests.value / totalTests.value) * 100) : 0
})

const overallStatus = computed(() => {
  if (overallProgress.value === 100) return 'success'
  if (overallProgress.value >= 80) return 'active'
  return 'exception'
})

const progressColor = computed(() => {
  if (overallProgress.value >= 90) return '#52c41a'
  if (overallProgress.value >= 70) return '#faad14'
  return '#ff4d4f'
})

const basicTestsPassRate = computed(() => {
  if (basicTestResults.value.length === 0) return 0
  const passed = basicTestResults.value.filter(t => t.success).length
  return Math.round((passed / basicTestResults.value.length) * 100)
})

const interactionTestsPassRate = computed(() => {
  if (interactionTestResults.value.length === 0) return 0
  const passed = interactionTestResults.value.filter(t => t.success).length
  return Math.round((passed / interactionTestResults.value.length) * 100)
})

const performanceTestsPassRate = computed(() => {
  if (performanceTestResults.value.length === 0) return 0
  const passed = performanceTestResults.value.filter(t => t.success).length
  return Math.round((passed / performanceTestResults.value.length) * 100)
})

const testCategories = computed<TestCategory[]>(() => [
  {
    name: '基础功能测试',
    results: basicTestResults.value,
    passRate: basicTestsPassRate.value
  },
  {
    name: '交互功能测试',
    results: interactionTestResults.value,
    passRate: interactionTestsPassRate.value
  },
  {
    name: '性能测试',
    results: performanceTestResults.value,
    passRate: performanceTestsPassRate.value
  },
  {
    name: '错误处理测试',
    results: errorHandlingTestResults.value,
    passRate: errorHandlingTestResults.value.length > 0 ?
      Math.round((errorHandlingTestResults.value.filter(t => t.success).length / errorHandlingTestResults.value.length) * 100) : 0
  }
])

// 表格列定义
const testResultColumns = [
  { title: '测试项', dataIndex: 'title', key: 'title' },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '状态', key: 'status', width: 80 },
  { title: '耗时', key: 'duration', width: 80 },
  { title: '操作', key: 'details', width: 80 }
]

// 工具函数
const measureTime = async (fn: () => Promise<any> | any): Promise<{ result: any, duration: number }> => {
  const start = performance.now()
  const result = await fn()
  const duration = Math.round(performance.now() - start)
  return { result, duration }
}

const createTestResult = (
  title: string,
  description: string,
  success: boolean,
  duration: number,
  details?: any,
  error?: string
): TestResult => ({
  title,
  description,
  success,
  duration,
  details,
  error
})

// 基础功能测试
const runBasicTests = async () => {
  console.log('开始基础功能测试...')
  basicTestResults.value = []

  try {
    // 测试1: 数据转换功能
    const { result: transformResult, duration: transformDuration } = await measureTime(async () => {
      const sampleData = createSampleLineageData('basic')
      return transformToG6Data(sampleData)
    })

    basicTestResults.value.push(createTestResult(
      '数据转换功能',
      '测试血缘数据到G6数据的转换',
      transformResult && transformResult.nodes && transformResult.edges,
      transformDuration,
      transformResult
    ))

    // 测试2: 数据验证功能
    const { result: validateResult, duration: validateDuration } = await measureTime(async () => {
      const sampleData = createSampleLineageData('basic')
      return validateLineageData(sampleData)
    })

    basicTestResults.value.push(createTestResult(
      '数据验证功能',
      '测试血缘数据的完整性验证',
      validateResult.isValid,
      validateDuration,
      validateResult
    ))

    // 测试3: 布局算法功能
    const { result: layoutResult, duration: layoutDuration } = await measureTime(async () => {
      const sampleData = createSampleLineageData('basic')
      const g6Data = transformToG6Data(sampleData)
      const layoutOptions: DagreLayoutOptions = {
        rankdir: 'LR',
        align: 'UL',
        nodesep: 80,
        ranksep: 150
      }
      return applyDagreLayout(g6Data, layoutOptions)
    })

    basicTestResults.value.push(createTestResult(
      '布局算法功能',
      '测试Dagre布局算法的应用',
      layoutResult && layoutResult.nodes && layoutResult.nodes.length > 0,
      layoutDuration,
      layoutResult
    ))

    // 测试4: G6元素注册
    const { result: registerResult, duration: registerDuration } = await measureTime(async () => {
      return registerAllGraphElements()
    })

    basicTestResults.value.push(createTestResult(
      'G6元素注册',
      '测试自定义节点和边的注册',
      registerResult === true,
      registerDuration
    ))

    message.success('基础功能测试完成')
  } catch (error) {
    console.error('基础功能测试失败:', error)
    message.error('基础功能测试失败')
  }
}

// 交互功能测试
const runInteractionTests = async () => {
  console.log('开始交互功能测试...')
  interactionTestResults.value = []

  try {
    // 测试1: 状态管理功能
    const { result: storeResult, duration: storeDuration } = await measureTime(async () => {
      const store = useLineageStore()
      const sampleData = createSampleLineageData('basic')
      store.setLineageData(sampleData)
      return store.lineageData
    })

    interactionTestResults.value.push(createTestResult(
      '状态管理功能',
      '测试Pinia状态管理的数据存储和获取',
      storeResult && storeResult.nodes && storeResult.nodes.length > 0,
      storeDuration,
      storeResult
    ))

    // 测试2: 搜索功能
    const { result: searchResult, duration: searchDuration } = await measureTime(async () => {
      const store = useLineageStore()
      const sampleData = createSampleLineageData('basic')
      store.setLineageData(sampleData)
      // 模拟搜索功能
      const searchTerm = 'user'
      const results = sampleData.nodes.filter(node =>
        node.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        node.fieldName.toLowerCase().includes(searchTerm.toLowerCase())
      )
      return results
    })

    interactionTestResults.value.push(createTestResult(
      '搜索功能',
      '测试节点和字段的搜索功能',
      Array.isArray(searchResult) && searchResult.length >= 0,
      searchDuration,
      searchResult
    ))

    // 测试3: 主题切换功能
    const { result: themeResult, duration: themeDuration } = await measureTime(async () => {
      const store = useLineageStore()
      const originalTheme = store.theme.mode
      const newThemeMode = originalTheme === 'light' ? 'dark' : 'light'
      store.setTheme(newThemeMode)
      const newTheme = store.theme.mode
      store.setTheme(originalTheme) // 恢复原始主题
      return { originalTheme, newTheme, switched: originalTheme !== newTheme }
    })

    interactionTestResults.value.push(createTestResult(
      '主题切换功能',
      '测试深色/浅色主题的切换',
      themeResult.switched,
      themeDuration,
      themeResult
    ))

    // 测试4: 配置管理功能
    const { result: configResult, duration: configDuration } = await measureTime(async () => {
      const store = useLineageStore()
      const testConfig = {
        width: 800,
        height: 600,
        fitView: true
      }
      store.setGraphConfig(testConfig)
      return store.graphConfig
    })

    interactionTestResults.value.push(createTestResult(
      '配置管理功能',
      '测试图谱配置的设置和获取',
      configResult && configResult.width === 800,
      configDuration,
      configResult
    ))

    message.success('交互功能测试完成')
  } catch (error) {
    console.error('交互功能测试失败:', error)
    message.error('交互功能测试失败')
  }
}

// 性能测试
const runPerformanceTests = async () => {
  console.log('开始性能测试...')
  performanceTestResults.value = []

  try {
    // 测试1: 小数据量渲染性能
    const { result: smallDataResult, duration: smallDataDuration } = await measureTime(async () => {
      const sampleData = createSampleLineageData('basic')
      return transformToG6Data(sampleData)
    })

    performanceTestResults.value.push(createTestResult(
      '小数据量渲染性能',
      '测试小数据量(≤50节点)的渲染性能',
      smallDataDuration < 100, // 期望小于100ms
      smallDataDuration,
      { nodeCount: smallDataResult?.nodes?.length || 0 }
    ))

    // 测试2: 中等数据量渲染性能
    const { result: mediumDataResult, duration: mediumDataDuration } = await measureTime(async () => {
      const sampleData = createSampleLineageData('complex')
      return transformToG6Data(sampleData)
    })

    performanceTestResults.value.push(createTestResult(
      '中等数据量渲染性能',
      '测试中等数据量(50-200节点)的渲染性能',
      mediumDataDuration < 500, // 期望小于500ms
      mediumDataDuration,
      { nodeCount: mediumDataResult?.nodes?.length || 0 }
    ))

    // 测试3: 布局算法性能
    const { result: layoutPerfResult, duration: layoutPerfDuration } = await measureTime(async () => {
      const sampleData = createSampleLineageData('complex')
      const g6Data = transformToG6Data(sampleData)
      const layoutOptions: DagreLayoutOptions = {
        rankdir: 'LR',
        align: 'UL',
        nodesep: 80,
        ranksep: 150,
        enableOptimization: true
      }
      return applyDagreLayout(g6Data, layoutOptions)
    })

    performanceTestResults.value.push(createTestResult(
      '布局算法性能',
      '测试Dagre布局算法的性能',
      layoutPerfDuration < 1000, // 期望小于1秒
      layoutPerfDuration,
      { nodeCount: layoutPerfResult?.nodes?.length || 0 }
    ))

    // 测试4: 内存使用测试
    const { result: memoryResult, duration: memoryDuration } = await measureTime(async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0

      // 创建多个数据集进行测试
      for (let i = 0; i < 10; i++) {
        const sampleData = createSampleLineageData('complex')
        transformToG6Data(sampleData)
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      const memoryIncrease = (finalMemory - initialMemory) / (1024 * 1024) // MB

      return {
        initialMemory: initialMemory / (1024 * 1024),
        finalMemory: finalMemory / (1024 * 1024),
        memoryIncrease
      }
    })

    performanceTestResults.value.push(createTestResult(
      '内存使用测试',
      '测试多次数据转换的内存使用情况',
      memoryResult.memoryIncrease < 50, // 期望内存增长小于50MB
      memoryDuration,
      memoryResult
    ))

    // 更新性能指标
    performanceMetrics.avgRenderTime = Math.round((smallDataDuration + mediumDataDuration) / 2)
    performanceMetrics.maxRenderTime = Math.max(smallDataDuration, mediumDataDuration)
    performanceMetrics.memoryUsage = Math.round(memoryResult.memoryIncrease || 0)

    message.success('性能测试完成')
  } catch (error) {
    console.error('性能测试失败:', error)
    message.error('性能测试失败')
  }
}

// 错误处理测试
const runErrorHandlingTests = async () => {
  console.log('开始错误处理测试...')
  errorHandlingTestResults.value = []

  try {
    // 测试1: 无效数据处理
    const { result: invalidDataResult, duration: invalidDataDuration } = await measureTime(async () => {
      try {
        const invalidData = { nodes: null, edges: null, tables: {} } as any
        const result = validateLineageData(invalidData)
        return { success: !result.isValid, result }
      } catch (error) {
        return { success: true, error: error instanceof Error ? error.message : String(error) }
      }
    })

    errorHandlingTestResults.value.push(createTestResult(
      '无效数据处理',
      '测试对无效血缘数据的处理',
      invalidDataResult.success,
      invalidDataDuration,
      invalidDataResult
    ))

    // 测试2: 空数据处理
    const { result: emptyDataResult, duration: emptyDataDuration } = await measureTime(async () => {
      try {
        const emptyData = { nodes: [], edges: [], tables: {} }
        const result = transformToG6Data(emptyData)
        return { success: result && result.nodes && result.edges, result }
      } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : String(error) }
      }
    })

    errorHandlingTestResults.value.push(createTestResult(
      '空数据处理',
      '测试对空血缘数据的处理',
      emptyDataResult.success,
      emptyDataDuration,
      emptyDataResult
    ))

    // 测试3: SQL格式化错误处理
    const { result: sqlErrorResult, duration: sqlErrorDuration } = await measureTime(async () => {
      try {
        const invalidSql = 'INVALID SQL STATEMENT'
        const result = formatSql(invalidSql)
        return { success: typeof result === 'string', result }
      } catch (error) {
        return { success: true, error: error instanceof Error ? error.message : String(error) } // 期望捕获错误
      }
    })

    errorHandlingTestResults.value.push(createTestResult(
      'SQL格式化错误处理',
      '测试对无效SQL语句的错误处理',
      sqlErrorResult.success,
      sqlErrorDuration,
      sqlErrorResult
    ))

    // 测试4: 布局算法错误处理
    const { result: layoutErrorResult, duration: layoutErrorDuration } = await measureTime(async () => {
      try {
        const invalidData = { nodes: [{ id: 'invalid' }], edges: [] } as any
        const result = applyDagreLayout(invalidData, { rankdir: 'LR' })
        return { success: true, result }
      } catch (error) {
        return { success: true, error: error instanceof Error ? error.message : String(error) } // 期望有降级处理
      }
    })

    errorHandlingTestResults.value.push(createTestResult(
      '布局算法错误处理',
      '测试布局算法的错误处理和降级机制',
      layoutErrorResult.success,
      layoutErrorDuration,
      layoutErrorResult
    ))

    message.success('错误处理测试完成')
  } catch (error) {
    console.error('错误处理测试失败:', error)
    message.error('错误处理测试失败')
  }
}

// 运行全部测试
const runAllTests = async () => {
  isRunningAll.value = true

  try {
    message.info('开始运行全部测试...')

    await runBasicTests()
    await runInteractionTests()
    await runPerformanceTests()
    await runErrorHandlingTests()

    message.success(`全部测试完成！通过率: ${overallProgress.value}%`)
  } catch (error) {
    console.error('测试执行失败:', error)
    message.error('测试执行失败')
  } finally {
    isRunningAll.value = false
  }
}

// 清空测试结果
const clearResults = () => {
  basicTestResults.value = []
  interactionTestResults.value = []
  performanceTestResults.value = []
  errorHandlingTestResults.value = []

  // 重置性能指标
  Object.assign(performanceMetrics, {
    avgRenderTime: 0,
    maxRenderTime: 0,
    memoryUsage: 0,
    avgResponseTime: 0,
    fps: 0,
    cpuUsage: 0
  })

  message.info('测试结果已清空')
}

// 导出测试报告
const exportResults = () => {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: totalTests.value,
      passedTests: passedTests.value,
      failedTests: failedTests.value,
      overallProgress: overallProgress.value
    },
    categories: testCategories.value,
    performanceMetrics: performanceMetrics
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `integration-test-report-${new Date().toISOString().slice(0, 19)}.json`
  a.click()
  URL.revokeObjectURL(url)

  message.success('测试报告已导出')
}

// 显示测试详情
const showTestDetails = (record: TestResult) => {
  Modal.info({
    title: `测试详情: ${record.title}`,
    content: h('div', [
      h('p', `描述: ${record.description}`),
      h('p', `状态: ${record.success ? '通过' : '失败'}`),
      h('p', `耗时: ${record.duration}ms`),
      record.error && h('p', { style: 'color: red' }, `错误: ${record.error}`),
      record.details && h('pre', { style: 'background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow: auto' },
        JSON.stringify(record.details, null, 2))
    ]),
    width: 600
  })
}

// 组件挂载时初始化
onMounted(() => {
  console.log('集成测试页面已加载')
})
</script>

<style scoped>
.integration-test-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 28px;
}

.test-header p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 16px;
}

.test-progress {
  margin-top: 16px;
}

.progress-stats {
  display: flex;
  gap: 32px;
  margin-top: 16px;
}

.test-controls {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-results {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.overview-content {
  padding: 24px;
}

.test-category {
  height: 100%;
}

.category-stats {
  margin-bottom: 16px;
  text-align: center;
}

.test-pass {
  color: #52c41a;
}

.test-fail {
  color: #ff4d4f;
}

.details-content {
  padding: 24px;
}

.performance-content {
  padding: 24px;
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .integration-test-container {
    padding: 16px;
  }

  .test-header {
    padding: 16px;
  }

  .test-header h1 {
    font-size: 24px;
  }

  .progress-stats {
    flex-direction: column;
    gap: 16px;
  }

  .overview-content .ant-col {
    margin-bottom: 16px;
  }
}

/* 动画效果 */
.test-category {
  transition: all 0.3s ease;
}

.test-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 进度条颜色 */
.ant-progress-success-bg {
  background-color: #52c41a !important;
}

.ant-progress-exception-bg {
  background-color: #ff4d4f !important;
}

/* 统计数字样式 */
.ant-statistic-content-value {
  font-weight: 600;
}

/* 表格样式优化 */
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px 16px;
}

/* 折叠面板样式 */
.ant-collapse-header {
  font-weight: 500;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}
</style>
