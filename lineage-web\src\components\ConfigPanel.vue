<template>
  <a-drawer
    v-model:open="visible"
    title="配置管理"
    placement="right"
    width="400"
    :closable="true"
    @close="handleClose"
  >
    <div class="config-panel">
      <!-- 主题设置 -->
      <a-card title="主题设置" size="small" class="config-section">
        <a-form layout="vertical">
          <a-form-item label="主题模式">
            <a-radio-group v-model:value="localConfig.theme" @change="handleConfigChange">
              <a-radio value="light">浅色主题</a-radio>
              <a-radio value="dark">深色主题</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 布局设置 -->
      <a-card title="布局设置" size="small" class="config-section">
        <a-form layout="vertical">
          <a-form-item label="布局方向">
            <a-select v-model:value="localConfig.layoutDirection" @change="handleConfigChange">
              <a-select-option value="LR">从左到右</a-select-option>
              <a-select-option value="TB">从上到下</a-select-option>
              <a-select-option value="RL">从右到左</a-select-option>
              <a-select-option value="BT">从下到上</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showFieldTypes" @change="handleConfigChange">
              显示字段类型
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showTableComments" @change="handleConfigChange">
              显示表注释
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showFieldDescriptions" @change="handleConfigChange">
              显示字段描述
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showDataTypes" @change="handleConfigChange">
              显示数据类型
            </a-checkbox>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 性能设置 -->
      <a-card title="性能设置" size="small" class="config-section">
        <a-form layout="vertical">
          <a-form-item label="性能模式">
            <a-select v-model:value="localConfig.performanceMode" @change="handleConfigChange">
              <a-select-option value="normal">标准模式</a-select-option>
              <a-select-option value="optimized">优化模式</a-select-option>
              <a-select-option value="extreme">极速模式</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.enableVirtualRendering" @change="handleConfigChange">
              启用虚拟渲染
            </a-checkbox>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 图谱设置 -->
      <a-card title="图谱设置" size="small" class="config-section">
        <a-form layout="vertical">
          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showFieldLevelLineage" @change="handleConfigChange">
              显示字段级血缘
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showCompleteLineage" @change="handleConfigChange">
              显示完整血缘链路
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.showMiniMap" @change="handleConfigChange">
              显示缩略图
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.enableTooltips" @change="handleConfigChange">
              启用提示框
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="localConfig.autoSave" @change="handleConfigChange">
              自动保存配置
            </a-checkbox>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 操作按钮 -->
      <div class="config-actions">
        <a-space direction="vertical" style="width: 100%">
          <a-button type="primary" block @click="saveConfig" :loading="saving">
            <SaveOutlined />
            保存配置
          </a-button>

          <a-button block @click="resetConfig">
            <ReloadOutlined />
            重置为默认
          </a-button>

          <a-button block @click="exportConfig">
            <ExportOutlined />
            导出配置
          </a-button>

          <a-upload
            :show-upload-list="false"
            :before-upload="importConfig"
            accept=".json"
          >
            <a-button block>
              <ImportOutlined />
              导入配置
            </a-button>
          </a-upload>
        </a-space>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons-vue'
import type { UserConfig } from '@/utils/configManager'
import { configManager, defaultConfig, configUtils } from '@/utils/configManager'
import { useLineageStore } from '@/stores/lineageStore'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'config-changed': [config: UserConfig]
}>()

// Store
const lineageStore = useLineageStore()

// 状态
const saving = ref(false)
const localConfig = reactive<UserConfig>({ ...defaultConfig })

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleConfigChange = () => {
  // 实时应用配置变化
  emit('config-changed', { ...localConfig })

  // 如果启用自动保存，则自动保存配置
  if (localConfig.autoSave) {
    saveConfig()
  }
}

const saveConfig = async () => {
  try {
    saving.value = true

    // 保存到配置管理器
    await configManager.updateConfig(localConfig)

    // 应用主题
    configUtils.applyTheme(localConfig.theme)

    // 更新状态管理
    lineageStore.setTheme(localConfig.theme)

    message.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
    message.error('配置保存失败')
  } finally {
    saving.value = false
  }
}

const resetConfig = () => {
  Object.assign(localConfig, defaultConfig)
  handleConfigChange()
  message.info('已重置为默认配置')
}

const exportConfig = () => {
  try {
    const configJson = JSON.stringify(localConfig, null, 2)
    const blob = new Blob([configJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = `lineage-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
    message.success('配置导出成功')
  } catch (error) {
    console.error('导出配置失败:', error)
    message.error('配置导出失败')
  }
}

const importConfig = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const configData = JSON.parse(e.target?.result as string)

        // 验证配置格式
        if (configData && typeof configData === 'object') {
          Object.assign(localConfig, defaultConfig, configData)
          handleConfigChange()
          message.success('配置导入成功')
          resolve(true)
        } else {
          throw new Error('配置文件格式无效')
        }
      } catch (error) {
        console.error('导入配置失败:', error)
        message.error('配置文件格式无效')
        reject(error)
      }
    }

    reader.onerror = () => {
      message.error('读取配置文件失败')
      reject(new Error('读取文件失败'))
    }

    reader.readAsText(file)
  })
}

// 初始化配置
const initConfig = async () => {
  try {
    const savedConfig = await configManager.getConfig()
    Object.assign(localConfig, savedConfig)
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

// 监听visible变化，初始化配置
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      initConfig()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
/* Modern config panel with clean design */
.config-panel {
  padding: 0;
  background: var(--color-background);
}

.config-section {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: 0 2px 8px var(--color-shadow-light);
  border: 1px solid var(--color-border-light);
}

.config-section:last-of-type {
  margin-bottom: var(--spacing-xxl);
}

.config-actions {
  border-top: 1px solid var(--color-border-light);
  padding-top: var(--spacing-lg);
  background: var(--color-surface-variant);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

/* Enhanced card styling */
:deep(.ant-card) {
  border: none;
  box-shadow: none;
  background: var(--color-surface);
}

:deep(.ant-card-head) {
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
  min-height: auto;
  background: linear-gradient(135deg, var(--color-primary-subtle), var(--color-secondary-subtle));
  border-bottom: 1px solid var(--color-border-light);
}

:deep(.ant-card-head-title) {
  font-weight: 600;
  color: var(--color-heading);
  font-size: 15px;
}

:deep(.ant-card-body) {
  padding: var(--spacing-lg);
  background: var(--color-surface);
}

/* Form styling */
:deep(.ant-form-item) {
  margin-bottom: var(--spacing-lg);
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}

:deep(.ant-form-item-label) {
  padding-bottom: var(--spacing-sm);
}

:deep(.ant-form-item-label > label) {
  color: var(--color-text-secondary);
  font-weight: 500;
  font-size: 14px;
}

/* Radio and checkbox styling */
:deep(.ant-radio-group) {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

:deep(.ant-radio-wrapper) {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-small);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.ant-radio-wrapper:hover) {
  background-color: var(--color-primary-subtle);
}

:deep(.ant-checkbox-wrapper) {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-small);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.ant-checkbox-wrapper:hover) {
  background-color: var(--color-primary-subtle);
}

/* Select styling */
:deep(.ant-select) {
  border-radius: var(--border-radius-medium);
}

:deep(.ant-select-selector) {
  border-color: var(--color-border-light) !important;
  border-radius: var(--border-radius-medium) !important;
}

:deep(.ant-select-focused .ant-select-selector) {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px var(--color-primary-subtle) !important;
}

/* Button styling */
:deep(.ant-btn) {
  border-radius: var(--border-radius-medium);
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border: none;
  box-shadow: 0 2px 8px var(--color-shadow-medium);
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--color-shadow-heavy);
}

:deep(.ant-btn:not(.ant-btn-primary)) {
  border-color: var(--color-border-light);
  color: var(--color-text-secondary);
}

:deep(.ant-btn:not(.ant-btn-primary):hover) {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background-color: var(--color-primary-subtle);
}

/* Upload button styling */
:deep(.ant-upload) {
  width: 100%;
}

:deep(.ant-upload .ant-btn) {
  width: 100%;
}
</style>
