/**
 * 布局测试工具
 * 用于验证左右面板的宽度比例和溢出控制
 */

interface TestResult {
  success: boolean
  issues: string[]
  measurements?: any
}

export function testLayoutProportions(): TestResult {
  console.log('🔍 开始布局比例测试...')
  
  const results: TestResult = {
    success: true,
    issues: [],
    measurements: {}
  }
  
  try {
    // 获取布局容器
    const layoutContainer = document.querySelector('.layout-container')
    if (!layoutContainer) {
      results.success = false
      results.issues.push('未找到布局容器 .layout-container')
      return results
    }
    
    // 获取左右面板
    const leftPanel = document.querySelector('.left-panel')
    const rightLayout = document.querySelector('.right-layout')
    
    if (!leftPanel || !rightLayout) {
      results.success = false
      results.issues.push('未找到左侧面板或右侧面板')
      return results
    }
    
    // 测量容器和面板尺寸
    const containerRect = layoutContainer.getBoundingClientRect()
    const leftRect = leftPanel.getBoundingClientRect()
    const rightRect = rightLayout.getBoundingClientRect()
    
    results.measurements = {
      containerWidth: containerRect.width,
      leftWidth: leftRect.width,
      rightWidth: rightRect.width,
      leftPercentage: (leftRect.width / containerRect.width * 100).toFixed(2),
      rightPercentage: (rightRect.width / containerRect.width * 100).toFixed(2)
    }
    
    console.log('📊 布局测量结果:', results.measurements)
    
    // 验证40:60比例（允许1%的误差）
    const expectedLeftPercentage = 40
    const expectedRightPercentage = 60
    const tolerance = 1
    
    const actualLeftPercentage = parseFloat(results.measurements.leftPercentage)
    const actualRightPercentage = parseFloat(results.measurements.rightPercentage)
    
    if (Math.abs(actualLeftPercentage - expectedLeftPercentage) > tolerance) {
      results.success = false
      results.issues.push(`左侧面板宽度比例不正确: 期望${expectedLeftPercentage}%, 实际${actualLeftPercentage}%`)
    }
    
    if (Math.abs(actualRightPercentage - expectedRightPercentage) > tolerance) {
      results.success = false
      results.issues.push(`右侧面板宽度比例不正确: 期望${expectedRightPercentage}%, 实际${actualRightPercentage}%`)
    }
    
    // 检查是否有溢出
    const hasOverflow = checkForOverflow()
    if (hasOverflow.length > 0) {
      results.success = false
      results.issues.push(`检测到溢出元素: ${hasOverflow.join(', ')}`)
    }
    
    // 检查响应式行为
    const responsiveTest = testResponsiveBehavior()
    if (!responsiveTest.success) {
      results.issues.push(...responsiveTest.issues)
    }
    
  } catch (error: any) {
    results.success = false
    results.issues.push(`测试过程中发生错误: ${error.message}`)
  }
  
  return results
}

function checkForOverflow(): string[] {
  const overflowElements: string[] = []
  
  // 检查右侧面板内的元素是否溢出
  const rightPanel = document.querySelector('.right-layout')
  if (rightPanel) {
    const rightRect = rightPanel.getBoundingClientRect()
    const children = rightPanel.querySelectorAll('*')
    
    children.forEach(child => {
      const childRect = child.getBoundingClientRect()
      if (childRect.right > rightRect.right + 1) { // 允许1px误差
        overflowElements.push((child as HTMLElement).className || child.tagName)
      }
    })
  }
  
  return overflowElements
}

function testResponsiveBehavior(): TestResult {
  const results: TestResult = {
    success: true,
    issues: []
  }
  
  // 模拟不同屏幕尺寸
  const originalWidth = window.innerWidth
  
  try {
    // 测试小屏幕行为 (768px以下)
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 767
    })
    
    // 触发resize事件
    window.dispatchEvent(new Event('resize'))
    
    // 等待一小段时间让样式生效
    setTimeout(() => {
      const layoutContainer = document.querySelector('.layout-container')
      if (layoutContainer) {
        const computedStyle = window.getComputedStyle(layoutContainer)
        if (computedStyle.flexDirection !== 'column') {
          results.issues.push('小屏幕时布局未切换为垂直排列')
          results.success = false
        }
      }
    }, 100)
    
  } finally {
    // 恢复原始窗口宽度
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: originalWidth
    })
    window.dispatchEvent(new Event('resize'))
  }
  
  return results
}

export function runLayoutTests() {
  console.log('🧪 开始运行布局测试套件...')
  
  const testResults: Array<TestResult & { name: string }> = []
  
  // 测试1: 基本布局比例
  const proportionTest = testLayoutProportions()
  testResults.push({
    name: '布局比例测试',
    ...proportionTest
  })
  
  // 输出测试结果
  console.log('\n📋 布局测试结果汇总:')
  testResults.forEach(test => {
    const status = test.success ? '✅ 通过' : '❌ 失败'
    console.log(`${status} ${test.name}`)
    
    if (test.measurements) {
      console.log('  测量数据:', test.measurements)
    }
    
    if (test.issues.length > 0) {
      console.log('  问题列表:')
      test.issues.forEach(issue => {
        console.log(`    - ${issue}`)
      })
    }
  })
  
  const allPassed = testResults.every(test => test.success)
  console.log(`\n🎯 总体结果: ${allPassed ? '所有测试通过' : '存在问题需要修复'}`)
  
  return {
    success: allPassed,
    results: testResults
  }
}

// 暴露到全局，方便在浏览器控制台调用
declare global {
  interface Window {
    testLayout: () => any
    testLayoutProportions: () => TestResult
  }
}

if (typeof window !== 'undefined') {
  window.testLayout = runLayoutTests
  window.testLayoutProportions = testLayoutProportions
}
