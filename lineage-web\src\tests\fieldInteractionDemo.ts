/**
 * 字段交互功能演示
 * 展示字段悬浮、点击、详情面板、路径追踪等功能
 */

import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data } from '@/utils/graphDataTransform'

/**
 * 运行字段交互功能演示
 */
export function runFieldInteractionDemo() {
  console.log('🎯 字段交互功能演示开始')
  
  try {
    // 1. 创建示例数据
    console.log('\n📊 创建示例血缘数据...')
    const sampleData = createSampleLineageData()
    console.log('✅ 示例数据创建成功:', {
      tables: Object.keys(sampleData.tables).length,
      nodes: sampleData.nodes.length,
      edges: sampleData.edges.length
    })
    
    // 2. 转换为G6数据
    console.log('\n🔄 转换为G6图数据...')
    const g6Data = transformToG6Data(sampleData)
    console.log('✅ G6数据转换成功:', {
      nodes: g6Data.nodes.length,
      edges: g6Data.edges.length
    })
    
    // 3. 演示字段信息
    console.log('\n🔍 字段信息演示:')
    sampleData.nodes.slice(0, 3).forEach((field, index) => {
      console.log(`字段 ${index + 1}:`, {
        id: field.id,
        fieldName: field.fieldName,
        tableName: field.tableName,
        dataType: field.dataType,
        isKey: field.isKey,
        isNullable: field.isNullable
      })
    })
    
    // 4. 演示血缘关系
    console.log('\n🔗 血缘关系演示:')
    sampleData.edges.slice(0, 3).forEach((edge, index) => {
      console.log(`血缘关系 ${index + 1}:`, {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        transformType: edge.transformType,
        confidence: edge.confidence
      })
    })
    
    // 5. 演示交互功能说明
    console.log('\n🎮 交互功能说明:')
    console.log('1. 字段悬浮: 鼠标悬浮在字段上时显示Tooltip')
    console.log('2. 字段点击: 点击字段打开详情面板')
    console.log('3. 路径追踪: 在详情面板中点击"追踪血缘"按钮')
    console.log('4. 字段高亮: 悬浮和点击时自动高亮相关连线')
    console.log('5. 详情面板: 显示字段完整信息和血缘统计')
    
    // 6. 演示数据结构
    console.log('\n📋 数据结构演示:')
    const firstField = sampleData.nodes[0]
    console.log('字段数据结构:', JSON.stringify(firstField, null, 2))
    
    const firstEdge = sampleData.edges[0]
    console.log('血缘关系数据结构:', JSON.stringify(firstEdge, null, 2))
    
    console.log('\n🎉 字段交互功能演示完成!')
    
    return {
      success: true,
      data: {
        sampleData,
        g6Data,
        fieldCount: sampleData.nodes.length,
        edgeCount: sampleData.edges.length,
        tableCount: Object.keys(sampleData.tables).length
      }
    }
    
  } catch (error) {
    console.error('❌ 字段交互功能演示失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

/**
 * 演示字段交互事件处理
 */
export function demoFieldInteractionEvents() {
  console.log('🎭 字段交互事件演示')
  
  // 模拟字段悬浮事件
  const mockFieldHover = (fieldId: string, fieldData: any) => {
    console.log(`🖱️ 字段悬浮事件:`, {
      fieldId,
      fieldName: fieldData?.fieldName,
      tableName: fieldData?.tableName,
      action: '显示Tooltip，高亮字段和相关连线'
    })
  }
  
  // 模拟字段点击事件
  const mockFieldClick = (fieldId: string, fieldData: any) => {
    console.log(`👆 字段点击事件:`, {
      fieldId,
      fieldName: fieldData?.fieldName,
      tableName: fieldData?.tableName,
      action: '打开详情面板，设置选中状态'
    })
  }
  
  // 模拟路径追踪事件
  const mockTraceLineage = (fieldData: any) => {
    console.log(`🔍 路径追踪事件:`, {
      fieldId: fieldData?.id,
      fieldName: fieldData?.fieldName,
      action: '查找血缘路径，高亮相关路径'
    })
  }
  
  // 演示事件序列
  const sampleData = createSampleLineageData()
  const sampleField = sampleData.nodes[0]
  
  console.log('\n📝 事件序列演示:')
  console.log('1. 用户鼠标悬浮在字段上')
  mockFieldHover(sampleField.id, sampleField)
  
  console.log('\n2. 用户点击字段')
  mockFieldClick(sampleField.id, sampleField)
  
  console.log('\n3. 用户在详情面板中点击"追踪血缘"')
  mockTraceLineage(sampleField)
  
  console.log('\n✨ 事件演示完成!')
}

/**
 * 演示字段高亮功能
 */
export function demoFieldHighlight() {
  console.log('🌟 字段高亮功能演示')
  
  const sampleData = createSampleLineageData()
  const targetField = sampleData.nodes[0]
  
  // 查找相关连线
  const relatedEdges = sampleData.edges.filter(edge => 
    edge.source === targetField.id || edge.target === targetField.id
  )
  
  console.log(`\n🎯 目标字段: ${targetField.fieldName} (${targetField.tableName})`)
  console.log(`📊 相关连线数量: ${relatedEdges.length}`)
  
  relatedEdges.forEach((edge, index) => {
    console.log(`连线 ${index + 1}:`, {
      source: edge.source,
      target: edge.target,
      type: edge.transformType,
      confidence: edge.confidence
    })
  })
  
  console.log('\n💡 高亮效果说明:')
  console.log('- 字段背景色变化')
  console.log('- 相关连线颜色加深')
  console.log('- 连线宽度增加')
  console.log('- 添加阴影效果')
  
  return {
    targetField,
    relatedEdges,
    highlightCount: relatedEdges.length + 1 // 字段本身 + 相关连线
  }
}

// 将演示函数暴露到全局，方便在浏览器控制台中调用
if (typeof window !== 'undefined') {
  (window as any).demoFieldInteraction = runFieldInteractionDemo;
  (window as any).demoFieldEvents = demoFieldInteractionEvents;
  (window as any).demoFieldHighlight = demoFieldHighlight;
}
