/**
 * 用户体验评估工具
 * 用于评估界面易用性、交互流畅度和视觉效果
 */

// UX评估维度
export interface UXDimensions {
  usability: number        // 易用性
  accessibility: number   // 可访问性
  performance: number      // 性能体验
  visual: number          // 视觉设计
  interaction: number     // 交互体验
  content: number         // 内容质量
}

// UX评估结果
export interface UXEvaluationResult {
  overall: number
  dimensions: UXDimensions
  issues: UXIssue[]
  recommendations: UXRecommendation[]
  strengths: string[]
  weaknesses: string[]
}

// UX问题
export interface UXIssue {
  category: keyof UXDimensions
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  element?: string
  suggestion: string
}

// UX建议
export interface UXRecommendation {
  category: keyof UXDimensions
  priority: 'low' | 'medium' | 'high'
  title: string
  description: string
  impact: string
  effort: 'low' | 'medium' | 'high'
}

// 可访问性检查器
export class AccessibilityChecker {
  checkAccessibility(): { score: number; issues: UXIssue[] } {
    const issues: UXIssue[] = []
    let score = 100

    // 检查图片alt属性
    const images = document.querySelectorAll('img')
    images.forEach((img, index) => {
      if (!img.alt) {
        issues.push({
          category: 'accessibility',
          severity: 'medium',
          title: '图片缺少alt属性',
          description: `图片 ${index + 1} 缺少alt属性，影响屏幕阅读器用户体验`,
          element: `img:nth-child(${index + 1})`,
          suggestion: '为所有图片添加描述性的alt属性'
        })
        score -= 5
      }
    })

    // 检查按钮和链接的可访问性
    const buttons = document.querySelectorAll('button, a')
    buttons.forEach((btn, index) => {
      const text = btn.textContent?.trim()
      if (!text || text.length < 2) {
        issues.push({
          category: 'accessibility',
          severity: 'high',
          title: '按钮/链接缺少文本',
          description: `按钮/链接 ${index + 1} 缺少可读的文本内容`,
          element: `${btn.tagName.toLowerCase()}:nth-child(${index + 1})`,
          suggestion: '为所有交互元素提供清晰的文本标签'
        })
        score -= 10
      }
    })

    // 检查表单标签
    const inputs = document.querySelectorAll('input, select, textarea')
    inputs.forEach((input, index) => {
      const id = input.id
      const label = id ? document.querySelector(`label[for="${id}"]`) : null
      if (!label && !input.getAttribute('aria-label')) {
        issues.push({
          category: 'accessibility',
          severity: 'high',
          title: '表单元素缺少标签',
          description: `表单元素 ${index + 1} 缺少关联的标签`,
          element: `${input.tagName.toLowerCase()}:nth-child(${index + 1})`,
          suggestion: '为所有表单元素添加label或aria-label'
        })
        score -= 8
      }
    })

    // 检查颜色对比度（简化检查）
    const elements = document.querySelectorAll('*')
    let lowContrastCount = 0
    elements.forEach(el => {
      const styles = window.getComputedStyle(el)
      const color = styles.color
      const backgroundColor = styles.backgroundColor
      
      // 简化的对比度检查（实际应该使用WCAG算法）
      if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
        // 这里应该实现真正的对比度计算
        // 为了简化，我们只检查一些明显的低对比度情况
        if (color.includes('rgb(128') && backgroundColor.includes('rgb(128')) {
          lowContrastCount++
        }
      }
    })

    if (lowContrastCount > 0) {
      issues.push({
        category: 'accessibility',
        severity: 'medium',
        title: '颜色对比度可能不足',
        description: `发现 ${lowContrastCount} 个可能存在对比度问题的元素`,
        suggestion: '确保文本与背景的对比度至少为4.5:1'
      })
      score -= lowContrastCount * 2
    }

    return { score: Math.max(0, score), issues }
  }
}

// 性能体验检查器
export class PerformanceExperienceChecker {
  checkPerformanceUX(): { score: number; issues: UXIssue[] } {
    const issues: UXIssue[] = []
    let score = 100

    // 检查页面加载时间
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
    if (loadTime > 3000) {
      issues.push({
        category: 'performance',
        severity: 'high',
        title: '页面加载时间过长',
        description: `页面加载时间为 ${loadTime}ms，超过3秒`,
        suggestion: '优化资源加载，使用代码分割和懒加载'
      })
      score -= 20
    } else if (loadTime > 1500) {
      issues.push({
        category: 'performance',
        severity: 'medium',
        title: '页面加载时间较长',
        description: `页面加载时间为 ${loadTime}ms，建议优化`,
        suggestion: '进一步优化资源大小和加载策略'
      })
      score -= 10
    }

    // 检查DOM节点数量
    const nodeCount = document.querySelectorAll('*').length
    if (nodeCount > 1500) {
      issues.push({
        category: 'performance',
        severity: 'medium',
        title: 'DOM节点过多',
        description: `页面包含 ${nodeCount} 个DOM节点，可能影响性能`,
        suggestion: '简化DOM结构，使用虚拟滚动等技术'
      })
      score -= 15
    }

    // 检查图片优化
    const images = document.querySelectorAll('img')
    let unoptimizedImages = 0
    images.forEach(img => {
      if (img.src && !img.src.includes('.webp') && !img.src.includes('data:')) {
        unoptimizedImages++
      }
    })

    if (unoptimizedImages > 0) {
      issues.push({
        category: 'performance',
        severity: 'low',
        title: '图片格式未优化',
        description: `${unoptimizedImages} 张图片未使用现代格式`,
        suggestion: '使用WebP等现代图片格式'
      })
      score -= unoptimizedImages * 2
    }

    return { score: Math.max(0, score), issues }
  }
}

// 视觉设计检查器
export class VisualDesignChecker {
  checkVisualDesign(): { score: number; issues: UXIssue[] } {
    const issues: UXIssue[] = []
    let score = 100

    // 检查字体大小
    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6')
    let smallTextCount = 0
    textElements.forEach(el => {
      const fontSize = parseFloat(window.getComputedStyle(el).fontSize)
      if (fontSize < 12) {
        smallTextCount++
      }
    })

    if (smallTextCount > 0) {
      issues.push({
        category: 'visual',
        severity: 'medium',
        title: '字体过小',
        description: `${smallTextCount} 个元素的字体小于12px`,
        suggestion: '确保主要文本字体大小至少为14px'
      })
      score -= smallTextCount * 3
    }

    // 检查颜色一致性
    const colorElements = document.querySelectorAll('[style*="color"], [class*="color"]')
    const colors = new Set<string>()
    colorElements.forEach(el => {
      const color = window.getComputedStyle(el).color
      colors.add(color)
    })

    if (colors.size > 10) {
      issues.push({
        category: 'visual',
        severity: 'low',
        title: '颜色使用过多',
        description: `页面使用了 ${colors.size} 种不同颜色`,
        suggestion: '建立统一的颜色系统，限制颜色数量'
      })
      score -= 5
    }

    // 检查间距一致性
    const spacingElements = document.querySelectorAll('*')
    const margins = new Set<string>()
    const paddings = new Set<string>()
    
    spacingElements.forEach(el => {
      const styles = window.getComputedStyle(el)
      if (styles.margin !== '0px') margins.add(styles.margin)
      if (styles.padding !== '0px') paddings.add(styles.padding)
    })

    if (margins.size > 15 || paddings.size > 15) {
      issues.push({
        category: 'visual',
        severity: 'low',
        title: '间距不够统一',
        description: '页面使用了过多不同的间距值',
        suggestion: '建立统一的间距系统，使用8px基准'
      })
      score -= 5
    }

    return { score: Math.max(0, score), issues }
  }
}

// 交互体验检查器
export class InteractionChecker {
  checkInteraction(): { score: number; issues: UXIssue[] } {
    const issues: UXIssue[] = []
    let score = 100

    // 检查按钮大小
    const buttons = document.querySelectorAll('button, .ant-btn')
    let smallButtons = 0
    buttons.forEach(btn => {
      const rect = btn.getBoundingClientRect()
      if (rect.width < 44 || rect.height < 44) {
        smallButtons++
      }
    })

    if (smallButtons > 0) {
      issues.push({
        category: 'interaction',
        severity: 'medium',
        title: '按钮尺寸过小',
        description: `${smallButtons} 个按钮小于44x44px的最小触摸目标`,
        suggestion: '确保所有交互元素至少为44x44px'
      })
      score -= smallButtons * 5
    }

    // 检查加载状态
    const loadingElements = document.querySelectorAll('[loading], .ant-spin')
    if (loadingElements.length === 0) {
      // 检查是否有异步操作但没有加载状态
      const asyncButtons = document.querySelectorAll('button[type="submit"], .submit-btn')
      if (asyncButtons.length > 0) {
        issues.push({
          category: 'interaction',
          severity: 'low',
          title: '缺少加载状态指示',
          description: '异步操作缺少加载状态反馈',
          suggestion: '为所有异步操作添加加载状态指示'
        })
        score -= 10
      }
    }

    // 检查错误处理
    const errorElements = document.querySelectorAll('.ant-form-item-has-error, .error, [class*="error"]')
    const formElements = document.querySelectorAll('form, .ant-form')
    
    if (formElements.length > 0 && errorElements.length === 0) {
      issues.push({
        category: 'interaction',
        severity: 'medium',
        title: '缺少错误状态处理',
        description: '表单缺少错误状态的视觉反馈',
        suggestion: '为表单验证添加清晰的错误提示'
      })
      score -= 15
    }

    return { score: Math.max(0, score), issues }
  }
}

// UX评估器主类
export class UXEvaluator {
  private accessibilityChecker = new AccessibilityChecker()
  private performanceChecker = new PerformanceExperienceChecker()
  private visualChecker = new VisualDesignChecker()
  private interactionChecker = new InteractionChecker()

  async evaluateUX(): Promise<UXEvaluationResult> {
    // 等待页面完全加载
    await new Promise(resolve => {
      if (document.readyState === 'complete') {
        resolve(void 0)
      } else {
        window.addEventListener('load', () => resolve(void 0))
      }
    })

    const accessibilityResult = this.accessibilityChecker.checkAccessibility()
    const performanceResult = this.performanceChecker.checkPerformanceUX()
    const visualResult = this.visualChecker.checkVisualDesign()
    const interactionResult = this.interactionChecker.checkInteraction()

    // 计算各维度得分
    const dimensions: UXDimensions = {
      usability: Math.round((interactionResult.score + visualResult.score) / 2),
      accessibility: accessibilityResult.score,
      performance: performanceResult.score,
      visual: visualResult.score,
      interaction: interactionResult.score,
      content: this.evaluateContent()
    }

    // 计算总体得分
    const overall = Math.round(
      (dimensions.usability * 0.25 +
       dimensions.accessibility * 0.2 +
       dimensions.performance * 0.2 +
       dimensions.visual * 0.15 +
       dimensions.interaction * 0.15 +
       dimensions.content * 0.05)
    )

    // 合并所有问题
    const issues = [
      ...accessibilityResult.issues,
      ...performanceResult.issues,
      ...visualResult.issues,
      ...interactionResult.issues
    ]

    // 生成建议
    const recommendations = this.generateRecommendations(dimensions, issues)
    
    // 识别优势和劣势
    const strengths = this.identifyStrengths(dimensions)
    const weaknesses = this.identifyWeaknesses(dimensions)

    return {
      overall,
      dimensions,
      issues,
      recommendations,
      strengths,
      weaknesses
    }
  }

  private evaluateContent(): number {
    // 简化的内容质量评估
    let score = 100

    // 检查文本内容
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span')
    let emptyElements = 0
    textElements.forEach(el => {
      if (!el.textContent?.trim()) {
        emptyElements++
      }
    })

    score -= emptyElements * 2

    // 检查标题层级
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    if (headings.length === 0) {
      score -= 20
    }

    return Math.max(0, score)
  }

  private generateRecommendations(dimensions: UXDimensions, issues: UXIssue[]): UXRecommendation[] {
    const recommendations: UXRecommendation[] = []

    // 基于得分生成建议
    if (dimensions.accessibility < 80) {
      recommendations.push({
        category: 'accessibility',
        priority: 'high',
        title: '提升可访问性',
        description: '改善屏幕阅读器支持和键盘导航',
        impact: '提升残障用户体验，符合WCAG标准',
        effort: 'medium'
      })
    }

    if (dimensions.performance < 70) {
      recommendations.push({
        category: 'performance',
        priority: 'high',
        title: '优化性能体验',
        description: '减少加载时间，提升交互响应速度',
        impact: '显著提升用户满意度和留存率',
        effort: 'high'
      })
    }

    if (dimensions.visual < 75) {
      recommendations.push({
        category: 'visual',
        priority: 'medium',
        title: '改善视觉设计',
        description: '统一设计语言，提升视觉层次',
        impact: '提升品牌形象和用户信任度',
        effort: 'medium'
      })
    }

    return recommendations
  }

  private identifyStrengths(dimensions: UXDimensions): string[] {
    const strengths: string[] = []

    if (dimensions.accessibility >= 90) strengths.push('优秀的可访问性支持')
    if (dimensions.performance >= 85) strengths.push('出色的性能表现')
    if (dimensions.visual >= 85) strengths.push('统一的视觉设计')
    if (dimensions.interaction >= 85) strengths.push('流畅的交互体验')
    if (dimensions.usability >= 85) strengths.push('良好的易用性')

    return strengths
  }

  private identifyWeaknesses(dimensions: UXDimensions): string[] {
    const weaknesses: string[] = []

    if (dimensions.accessibility < 70) weaknesses.push('可访问性需要改善')
    if (dimensions.performance < 70) weaknesses.push('性能体验有待提升')
    if (dimensions.visual < 70) weaknesses.push('视觉设计不够统一')
    if (dimensions.interaction < 70) weaknesses.push('交互体验需要优化')
    if (dimensions.usability < 70) weaknesses.push('易用性有改进空间')

    return weaknesses
  }
}

// 导出单例实例
export const uxEvaluator = new UXEvaluator()
