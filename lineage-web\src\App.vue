<script setup lang="ts">
import { RouterView } from 'vue-router'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

// 全局错误处理
const handleGlobalError = (error: Error, errorInfo: any) => {
  console.error('App level error:', error)
  // 这里可以集成错误监控服务，如 Sentry
}

const handleErrorRetry = () => {
  // 重新加载应用
  window.location.reload()
}
</script>

<template>
  <div id="app">
    <ErrorBoundary
      fallback-title="应用加载失败"
      fallback-message="抱歉，应用遇到了一些问题。请尝试刷新页面或联系技术支持。"
      :enable-error-reporting="true"
      @error="handleGlobalError"
      @retry="handleErrorRetry"
    >
      <RouterView />
    </ErrorBoundary>
  </div>
</template>

<style>
/* Global app container with clean white background */
#app {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: var(--color-background);
  color: var(--color-text);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

/* Ensure consistent box-sizing and reset */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  background: var(--color-background);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar styling for consistency */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface-variant);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-hover);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-tertiary);
}
</style>
