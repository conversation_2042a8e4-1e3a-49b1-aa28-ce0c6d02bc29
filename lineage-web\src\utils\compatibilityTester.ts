/**
 * 兼容性测试工具
 * 用于检测浏览器兼容性、设备支持和功能可用性
 */

// 浏览器信息接口
export interface BrowserInfo {
  name: string
  version: string
  engine: string
  platform: string
  mobile: boolean
  userAgent: string
}

// 设备信息接口
export interface DeviceInfo {
  type: 'desktop' | 'tablet' | 'mobile'
  screenWidth: number
  screenHeight: number
  devicePixelRatio: number
  touchSupport: boolean
  orientation: 'portrait' | 'landscape'
}

// 功能支持检测结果
export interface FeatureSupport {
  canvas: boolean
  webgl: boolean
  svg: boolean
  css3: boolean
  es6: boolean
  modules: boolean
  webWorkers: boolean
  localStorage: boolean
  sessionStorage: boolean
  indexedDB: boolean
  webAssembly: boolean
  intersectionObserver: boolean
  resizeObserver: boolean
  mutationObserver: boolean
}

// 兼容性测试结果
export interface CompatibilityTestResult {
  browser: BrowserInfo
  device: DeviceInfo
  features: FeatureSupport
  issues: string[]
  recommendations: string[]
  score: number
  passed: boolean
}

// 浏览器检测
export function detectBrowser(): BrowserInfo {
  const ua = navigator.userAgent
  const platform = navigator.platform
  
  let name = 'Unknown'
  let version = 'Unknown'
  let engine = 'Unknown'
  
  // 检测浏览器
  if (ua.includes('Chrome') && !ua.includes('Edg')) {
    name = 'Chrome'
    const match = ua.match(/Chrome\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
    engine = 'Blink'
  } else if (ua.includes('Firefox')) {
    name = 'Firefox'
    const match = ua.match(/Firefox\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
    engine = 'Gecko'
  } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
    name = 'Safari'
    const match = ua.match(/Version\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
    engine = 'WebKit'
  } else if (ua.includes('Edg')) {
    name = 'Edge'
    const match = ua.match(/Edg\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
    engine = 'Blink'
  } else if (ua.includes('Opera') || ua.includes('OPR')) {
    name = 'Opera'
    const match = ua.match(/(?:Opera|OPR)\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
    engine = 'Blink'
  }
  
  const mobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua)
  
  return {
    name,
    version,
    engine,
    platform,
    mobile,
    userAgent: ua
  }
}

// 设备信息检测
export function detectDevice(): DeviceInfo {
  const width = window.screen.width
  const height = window.screen.height
  const pixelRatio = window.devicePixelRatio || 1
  
  let type: 'desktop' | 'tablet' | 'mobile' = 'desktop'
  
  if (width <= 768) {
    type = 'mobile'
  } else if (width <= 1024) {
    type = 'tablet'
  }
  
  const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  const orientation = width > height ? 'landscape' : 'portrait'
  
  return {
    type,
    screenWidth: width,
    screenHeight: height,
    devicePixelRatio: pixelRatio,
    touchSupport,
    orientation
  }
}

// 功能支持检测
export function detectFeatureSupport(): FeatureSupport {
  return {
    canvas: !!document.createElement('canvas').getContext,
    webgl: !!document.createElement('canvas').getContext('webgl'),
    svg: !!document.createElementNS && !!document.createElementNS('http://www.w3.org/2000/svg', 'svg').createSVGRect,
    css3: CSS.supports('display', 'flex'),
    es6: (() => {
      try {
        return new Function('() => {}'), true
      } catch (e) {
        return false
      }
    })(),
    modules: 'noModule' in HTMLScriptElement.prototype,
    webWorkers: typeof Worker !== 'undefined',
    localStorage: (() => {
      try {
        return typeof localStorage !== 'undefined' && localStorage !== null
      } catch (e) {
        return false
      }
    })(),
    sessionStorage: (() => {
      try {
        return typeof sessionStorage !== 'undefined' && sessionStorage !== null
      } catch (e) {
        return false
      }
    })(),
    indexedDB: !!window.indexedDB,
    webAssembly: typeof WebAssembly === 'object',
    intersectionObserver: 'IntersectionObserver' in window,
    resizeObserver: 'ResizeObserver' in window,
    mutationObserver: 'MutationObserver' in window
  }
}

// 兼容性测试器
export class CompatibilityTester {
  runCompatibilityTest(): CompatibilityTestResult {
    const browser = detectBrowser()
    const device = detectDevice()
    const features = detectFeatureSupport()
    
    const issues: string[] = []
    const recommendations: string[] = []
    
    // 检查浏览器兼容性
    this.checkBrowserCompatibility(browser, issues, recommendations)
    
    // 检查功能支持
    this.checkFeatureSupport(features, issues, recommendations)
    
    // 检查设备兼容性
    this.checkDeviceCompatibility(device, issues, recommendations)
    
    // 计算兼容性评分
    const score = this.calculateCompatibilityScore(browser, features, issues)
    const passed = score >= 80 && issues.length === 0
    
    return {
      browser,
      device,
      features,
      issues,
      recommendations,
      score,
      passed
    }
  }
  
  private checkBrowserCompatibility(browser: BrowserInfo, issues: string[], recommendations: string[]) {
    const version = parseFloat(browser.version)
    
    switch (browser.name) {
      case 'Chrome':
        if (version < 80) {
          issues.push('Chrome版本过低，建议升级到80+')
          recommendations.push('升级Chrome浏览器到最新版本')
        }
        break
      case 'Firefox':
        if (version < 75) {
          issues.push('Firefox版本过低，建议升级到75+')
          recommendations.push('升级Firefox浏览器到最新版本')
        }
        break
      case 'Safari':
        if (version < 13) {
          issues.push('Safari版本过低，建议升级到13+')
          recommendations.push('升级Safari浏览器到最新版本')
        }
        break
      case 'Edge':
        if (version < 80) {
          issues.push('Edge版本过低，建议升级到80+')
          recommendations.push('升级Edge浏览器到最新版本')
        }
        break
      case 'Unknown':
        issues.push('未知浏览器，可能存在兼容性问题')
        recommendations.push('建议使用Chrome、Firefox、Safari或Edge浏览器')
        break
    }
  }
  
  private checkFeatureSupport(features: FeatureSupport, issues: string[], recommendations: string[]) {
    if (!features.canvas) {
      issues.push('不支持Canvas，图谱无法正常渲染')
      recommendations.push('升级浏览器以支持Canvas API')
    }
    
    if (!features.svg) {
      issues.push('不支持SVG，部分图标可能无法显示')
      recommendations.push('升级浏览器以支持SVG')
    }
    
    if (!features.css3) {
      issues.push('不支持CSS3，样式可能异常')
      recommendations.push('升级浏览器以支持CSS3')
    }
    
    if (!features.es6) {
      issues.push('不支持ES6，JavaScript功能可能异常')
      recommendations.push('升级浏览器以支持ES6')
    }
    
    if (!features.localStorage) {
      issues.push('不支持localStorage，配置无法保存')
      recommendations.push('检查浏览器设置，启用本地存储')
    }
    
    if (!features.intersectionObserver) {
      recommendations.push('建议升级浏览器以支持IntersectionObserver，提升性能')
    }
    
    if (!features.resizeObserver) {
      recommendations.push('建议升级浏览器以支持ResizeObserver，提升响应式体验')
    }
  }
  
  private checkDeviceCompatibility(device: DeviceInfo, issues: string[], recommendations: string[]) {
    if (device.screenWidth < 768) {
      recommendations.push('小屏幕设备，建议使用横屏模式以获得更好体验')
    }
    
    if (device.devicePixelRatio > 2) {
      recommendations.push('高分辨率屏幕，图谱渲染可能需要更多资源')
    }
    
    if (device.type === 'mobile' && !device.touchSupport) {
      issues.push('移动设备不支持触摸，交互可能受限')
    }
  }
  
  private calculateCompatibilityScore(browser: BrowserInfo, features: FeatureSupport, issues: string[]): number {
    let score = 100
    
    // 浏览器评分 (40%)
    const browserScore = this.getBrowserScore(browser)
    score = score * 0.6 + browserScore * 0.4
    
    // 功能支持评分 (40%)
    const featureScore = this.getFeatureScore(features)
    score = score * 0.6 + featureScore * 0.4
    
    // 问题扣分 (20%)
    score -= issues.length * 10
    
    return Math.max(0, Math.round(score))
  }
  
  private getBrowserScore(browser: BrowserInfo): number {
    const version = parseFloat(browser.version)
    
    switch (browser.name) {
      case 'Chrome':
        return version >= 90 ? 100 : version >= 80 ? 90 : 70
      case 'Firefox':
        return version >= 85 ? 100 : version >= 75 ? 90 : 70
      case 'Safari':
        return version >= 14 ? 100 : version >= 13 ? 90 : 70
      case 'Edge':
        return version >= 90 ? 100 : version >= 80 ? 90 : 70
      default:
        return 50
    }
  }
  
  private getFeatureScore(features: FeatureSupport): number {
    const requiredFeatures = ['canvas', 'svg', 'css3', 'es6', 'localStorage']
    const optionalFeatures = ['webgl', 'webWorkers', 'intersectionObserver', 'resizeObserver']
    
    let score = 0
    
    // 必需功能 (80%)
    const supportedRequired = requiredFeatures.filter(feature => features[feature as keyof FeatureSupport]).length
    score += (supportedRequired / requiredFeatures.length) * 80
    
    // 可选功能 (20%)
    const supportedOptional = optionalFeatures.filter(feature => features[feature as keyof FeatureSupport]).length
    score += (supportedOptional / optionalFeatures.length) * 20
    
    return Math.round(score)
  }
}

// 导出单例实例
export const compatibilityTester = new CompatibilityTester()
