<template>
  <div class="g6-sample-test">
    <div class="test-header">
      <h2>G6示例数据测试</h2>
      <p>测试新的G6 dagre-combo风格示例数据生成功能</p>
    </div>
    
    <div class="test-controls">
      <a-space>
        <a-button type="primary" @click="loadG6Sample" :loading="loading">
          加载G6示例数据
        </a-button>
        <a-button @click="loadLegacySample" :loading="loading">
          加载传统示例数据
        </a-button>
        <a-button @click="clearData">
          清空数据
        </a-button>
        <a-button @click="showDataStructure">
          显示数据结构
        </a-button>
      </a-space>
    </div>
    
    <div class="test-results" v-if="testResults.length > 0">
      <h3>测试结果</h3>
      <div class="results-list">
        <div 
          v-for="(result, index) in testResults" 
          :key="index"
          :class="['result-item', result.type]"
        >
          <div class="result-header">
            <span class="result-icon">
              {{ result.type === 'success' ? '✅' : result.type === 'error' ? '❌' : 'ℹ️' }}
            </span>
            <span class="result-title">{{ result.title }}</span>
            <span class="result-time">{{ result.time }}</span>
          </div>
          <div class="result-content" v-if="result.content">
            <pre>{{ result.content }}</pre>
          </div>
        </div>
      </div>
    </div>
    
    <div class="data-display" v-if="currentData">
      <h3>当前数据概览</h3>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card title="基本统计" size="small">
            <p>表数量: {{ currentData.tables }}</p>
            <p>节点数量: {{ currentData.nodes }}</p>
            <p>边数量: {{ currentData.edges }}</p>
            <p>Combo数量: {{ currentData.combos }}</p>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="分层信息" size="small">
            <div v-for="combo in currentData.comboDetails" :key="combo.id">
              <a-tag :color="combo.color">{{ combo.label }}</a-tag>
              <span class="combo-count">({{ combo.count }}个表)</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="转换类型" size="small">
            <div v-for="transform in currentData.transformTypes" :key="transform.type">
              <a-tag>{{ transform.type }}</a-tag>
              <span class="transform-count">({{ transform.count }}条)</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
    
    <div class="graph-container" v-if="showGraph">
      <h3>图谱预览</h3>
      <div ref="graphContainer" class="graph-canvas"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { createSampleLineageData, createLegacySampleLineageData } from '@/utils/sqlParser'
import { createG6SampleLineageData } from '@/utils/g6SampleData'
import { transformToG6Data, getComboLabel, getComboColor } from '@/utils/graphDataTransform'
import type { LineageData, G6GraphData } from '@/types/lineage'

// 响应式数据
const loading = ref(false)
const showGraph = ref(false)
const graphContainer = ref<HTMLElement>()
const testResults = ref<Array<{
  type: 'success' | 'error' | 'info'
  title: string
  content?: string
  time: string
}>>([])

const currentData = ref<{
  tables: number
  nodes: number
  edges: number
  combos: number
  comboDetails: Array<{ id: string, label: string, color: string, count: number }>
  transformTypes: Array<{ type: string, count: number }>
} | null>(null)

// 添加测试结果
const addResult = (type: 'success' | 'error' | 'info', title: string, content?: any) => {
  testResults.value.push({
    type,
    title,
    content: content ? JSON.stringify(content, null, 2) : undefined,
    time: new Date().toLocaleTimeString()
  })
}

// 加载G6示例数据
const loadG6Sample = async () => {
  loading.value = true
  try {
    addResult('info', '开始加载G6示例数据...')
    
    // 生成G6示例数据
    const lineageData = createSampleLineageData()
    addResult('success', 'G6示例数据生成成功', {
      tables: Object.keys(lineageData.tables).length,
      nodes: lineageData.nodes.length,
      edges: lineageData.edges.length,
      version: lineageData.metadata?.version
    })
    
    // 转换为G6数据
    const g6Data = transformToG6Data(lineageData)
    addResult('success', 'G6数据转换成功', {
      nodes: g6Data.nodes.length,
      edges: g6Data.edges.length,
      combos: g6Data.combos?.length || 0
    })
    
    // 分析数据结构
    analyzeDataStructure(lineageData, g6Data)
    
    message.success('G6示例数据加载完成')
  } catch (error) {
    addResult('error', '加载G6示例数据失败', error)
    message.error('加载失败')
  } finally {
    loading.value = false
  }
}

// 加载传统示例数据
const loadLegacySample = async () => {
  loading.value = true
  try {
    addResult('info', '开始加载传统示例数据...')
    
    const lineageData = createLegacySampleLineageData()
    const g6Data = transformToG6Data(lineageData)
    
    addResult('success', '传统示例数据加载成功', {
      tables: Object.keys(lineageData.tables).length,
      nodes: lineageData.nodes.length,
      edges: lineageData.edges.length,
      combos: g6Data.combos?.length || 0
    })
    
    analyzeDataStructure(lineageData, g6Data)
    message.success('传统示例数据加载完成')
  } catch (error) {
    addResult('error', '加载传统示例数据失败', error)
    message.error('加载失败')
  } finally {
    loading.value = false
  }
}

// 分析数据结构
const analyzeDataStructure = (lineageData: LineageData, g6Data: G6GraphData) => {
  // 统计combo信息
  const comboMap = new Map<string, number>()
  Object.values(lineageData.tables).forEach(table => {
    if (table.combo) {
      comboMap.set(table.combo, (comboMap.get(table.combo) || 0) + 1)
    }
  })
  
  const comboDetails = Array.from(comboMap.entries()).map(([id, count]) => ({
    id,
    label: getComboLabel(id),
    color: getComboColor(id),
    count
  }))
  
  // 统计转换类型
  const transformMap = new Map<string, number>()
  lineageData.edges.forEach(edge => {
    if (edge.transformType) {
      transformMap.set(edge.transformType, (transformMap.get(edge.transformType) || 0) + 1)
    }
  })
  
  const transformTypes = Array.from(transformMap.entries()).map(([type, count]) => ({
    type,
    count
  }))
  
  currentData.value = {
    tables: Object.keys(lineageData.tables).length,
    nodes: lineageData.nodes.length,
    edges: lineageData.edges.length,
    combos: g6Data.combos?.length || 0,
    comboDetails,
    transformTypes
  }
  
  addResult('info', '数据结构分析完成', currentData.value)
}

// 清空数据
const clearData = () => {
  testResults.value = []
  currentData.value = null
  showGraph.value = false
  addResult('info', '数据已清空')
}

// 显示数据结构
const showDataStructure = () => {
  if (currentData.value) {
    console.log('📊 当前数据结构:', currentData.value)
    addResult('info', '数据结构已输出到控制台')
  } else {
    message.warning('请先加载数据')
  }
}
</script>

<style scoped>
.g6-sample-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 24px;
  text-align: center;
}

.test-header h2 {
  margin-bottom: 8px;
  color: #1890ff;
}

.test-controls {
  margin-bottom: 24px;
  text-align: center;
}

.test-results {
  margin-bottom: 24px;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.result-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.success {
  background-color: #f6ffed;
  border-left: 4px solid #52c41a;
}

.result-item.error {
  background-color: #fff2f0;
  border-left: 4px solid #ff4d4f;
}

.result-item.info {
  background-color: #e6f7ff;
  border-left: 4px solid #1890ff;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-icon {
  font-size: 16px;
}

.result-title {
  font-weight: 500;
  flex: 1;
}

.result-time {
  font-size: 12px;
  color: #666;
}

.result-content {
  margin-top: 8px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  font-size: 12px;
}

.result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.data-display {
  margin-bottom: 24px;
}

.combo-count,
.transform-count {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.graph-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.graph-canvas {
  width: 100%;
  height: 400px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}
</style>
