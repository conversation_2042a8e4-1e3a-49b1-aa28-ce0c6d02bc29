@import './base.css';

/* Global application styles for split-screen layout */
#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  background: var(--color-background);
  overflow: hidden;
}

/* Link styles with theme colors */
a {
  text-decoration: none;
  color: var(--color-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (hover: hover) {
  a:hover {
    color: var(--color-primary-dark);
    background-color: var(--color-primary-subtle);
  }
}

/* Remove default responsive behavior for split layout */
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

/* Ensure consistent box-sizing */
*, *::before, *::after {
  box-sizing: border-box;
}
