<template>
  <div class="layout-test-container">
    <a-card title="布局算法优化测试" class="test-card">
      <div class="test-controls">
        <a-space wrap>
          <a-button type="primary" @click="testBasicDagreLayout">
            测试基础Dagre布局
          </a-button>
          <a-button type="primary" @click="testOptimizedLayout">
            测试优化布局
          </a-button>
          <a-button type="primary" @click="testLayoutDirections">
            测试布局方向
          </a-button>
          <a-button type="primary" @click="testLayoutSpacing">
            测试布局间距
          </a-button>
          <a-button type="primary" @click="testLargeDataLayout">
            测试大数据量布局
          </a-button>
          <a-button @click="clearResults">清空结果</a-button>
        </a-space>
      </div>

      <div class="test-results">
        <a-list
          :data-source="testResults"
          :pagination="false"
          size="small"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <span :class="item.success ? 'success-title' : 'error-title'">
                    {{ item.title }}
                  </span>
                </template>
                <template #description>
                  {{ item.description }}
                  <div v-if="item.details" class="test-details">
                    <pre>{{ JSON.stringify(item.details, null, 2) }}</pre>
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-card>

    <!-- 布局参数配置面板 -->
    <a-card title="布局参数配置" class="config-card">
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="布局方向">
              <a-select v-model:value="layoutConfig.rankdir">
                <a-select-option value="LR">从左到右</a-select-option>
                <a-select-option value="TB">从上到下</a-select-option>
                <a-select-option value="RL">从右到左</a-select-option>
                <a-select-option value="BT">从下到上</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="节点间距">
              <a-input-number
                v-model:value="layoutConfig.nodesep"
                :min="20"
                :max="200"
                :step="10"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="层级间距">
              <a-input-number
                v-model:value="layoutConfig.ranksep"
                :min="50"
                :max="300"
                :step="10"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="边间距">
              <a-input-number
                v-model:value="layoutConfig.edgesep"
                :min="5"
                :max="50"
                :step="5"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="启用性能优化">
              <a-switch v-model:checked="layoutConfig.enableOptimization" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="大数据量阈值">
              <a-input-number
                v-model:value="layoutConfig.largeDataThreshold"
                :min="10"
                :max="1000"
                :step="10"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="">
              <a-button type="primary" @click="applyLayoutConfig">
                应用配置
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 性能统计面板 -->
    <a-card title="性能统计" class="stats-card">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="节点数量" :value="performanceStats.nodeCount" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="边数量" :value="performanceStats.edgeCount" />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="布局耗时"
            :value="performanceStats.layoutTime"
            suffix="ms"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="渲染耗时"
            :value="performanceStats.renderTime"
            suffix="ms"
          />
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import {
  applyDagreLayout,
  transformToG6Data,
  type DagreLayoutOptions
} from '@/utils/graphDataTransform'
import { createSampleLineageData } from '@/utils/sqlParser'
import type { G6GraphData } from '@/types/lineage'

// 测试结果
interface TestResult {
  title: string
  description: string
  success: boolean
  details?: any
}

const testResults = ref<TestResult[]>([])

// 布局配置
const layoutConfig = reactive<DagreLayoutOptions>({
  rankdir: 'LR',
  align: 'UL',
  nodesep: 80,
  ranksep: 150,
  edgesep: 10,
  enableOptimization: true,
  largeDataThreshold: 100
})

// 性能统计
const performanceStats = reactive({
  nodeCount: 0,
  edgeCount: 0,
  layoutTime: 0,
  renderTime: 0
})

// 添加测试结果
const addResult = (title: string, description: string, success: boolean, details?: any) => {
  testResults.value.unshift({
    title,
    description,
    success,
    details
  })
}

// 清空结果
const clearResults = () => {
  testResults.value = []
  performanceStats.nodeCount = 0
  performanceStats.edgeCount = 0
  performanceStats.layoutTime = 0
  performanceStats.renderTime = 0
}

// 测试基础Dagre布局
const testBasicDagreLayout = () => {
  try {
    addResult('开始基础Dagre布局测试', '测试基础的Dagre布局算法功能', true)

    const lineageData = createSampleLineageData()
    const g6Data = transformToG6Data(lineageData)

    const startTime = performance.now()
    const layoutedData = applyDagreLayout(g6Data, {
      rankdir: 'LR',
      nodesep: 80,
      ranksep: 150
    })
    const endTime = performance.now()

    const layoutTime = Math.round(endTime - startTime)

    // 验证布局结果
    const hasPositions = layoutedData.nodes.every(node =>
      typeof node.x === 'number' && typeof node.y === 'number'
    )

    performanceStats.nodeCount = layoutedData.nodes.length
    performanceStats.edgeCount = layoutedData.edges.length
    performanceStats.layoutTime = layoutTime

    addResult(
      hasPositions ? '✅ 基础Dagre布局测试通过' : '❌ 基础Dagre布局测试失败',
      `布局计算${hasPositions ? '成功' : '失败'}，耗时: ${layoutTime}ms`,
      hasPositions,
      {
        nodeCount: layoutedData.nodes.length,
        edgeCount: layoutedData.edges.length,
        layoutTime,
        hasPositions
      }
    )

  } catch (error) {
    addResult('❌ 基础Dagre布局测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

// 测试优化布局
const testOptimizedLayout = () => {
  try {
    addResult('开始优化布局测试', '测试大数据量时的布局优化功能', true)

    // 创建大数据量测试数据
    const largeData = createLargeDataset(150) // 超过阈值的数据量

    const startTime = performance.now()
    const layoutedData = applyDagreLayout(largeData, {
      rankdir: 'LR',
      enableOptimization: true,
      largeDataThreshold: 100
    })
    const endTime = performance.now()

    const layoutTime = Math.round(endTime - startTime)

    const hasPositions = layoutedData.nodes.every(node =>
      typeof node.x === 'number' && typeof node.y === 'number'
    )

    performanceStats.nodeCount = layoutedData.nodes.length
    performanceStats.edgeCount = layoutedData.edges.length
    performanceStats.layoutTime = layoutTime

    addResult(
      hasPositions ? '✅ 优化布局测试通过' : '❌ 优化布局测试失败',
      `大数据量布局${hasPositions ? '成功' : '失败'}，耗时: ${layoutTime}ms`,
      hasPositions,
      {
        nodeCount: layoutedData.nodes.length,
        edgeCount: layoutedData.edges.length,
        layoutTime,
        optimizationEnabled: true
      }
    )

  } catch (error) {
    addResult('❌ 优化布局测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

// 测试布局方向
const testLayoutDirections = () => {
  try {
    addResult('开始布局方向测试', '测试不同布局方向的效果', true)

    const lineageData = createSampleLineageData()
    const g6Data = transformToG6Data(lineageData)

    const directions = ['LR', 'TB', 'RL', 'BT'] as const

    directions.forEach(direction => {
      const startTime = performance.now()
      const layoutedData = applyDagreLayout(g6Data, { rankdir: direction })
      const endTime = performance.now()

      const layoutTime = Math.round(endTime - startTime)
      const hasPositions = layoutedData.nodes.every(node =>
        typeof node.x === 'number' && typeof node.y === 'number'
      )

      addResult(
        `✅ ${direction}方向布局`,
        `${direction}方向布局${hasPositions ? '成功' : '失败'}，耗时: ${layoutTime}ms`,
        hasPositions,
        { direction, layoutTime, hasPositions }
      )
    })

  } catch (error) {
    addResult('❌ 布局方向测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

// 测试布局间距
const testLayoutSpacing = () => {
  try {
    addResult('开始布局间距测试', '测试不同间距参数的效果', true)

    const lineageData = createSampleLineageData()
    const g6Data = transformToG6Data(lineageData)

    const spacingConfigs = [
      { nodesep: 50, ranksep: 100 },
      { nodesep: 80, ranksep: 150 },
      { nodesep: 120, ranksep: 200 }
    ]

    spacingConfigs.forEach((config, index) => {
      const startTime = performance.now()
      const layoutedData = applyDagreLayout(g6Data, config)
      const endTime = performance.now()

      const layoutTime = Math.round(endTime - startTime)
      const hasPositions = layoutedData.nodes.every(node =>
        typeof node.x === 'number' && typeof node.y === 'number'
      )

      addResult(
        `✅ 间距配置${index + 1}`,
        `节点间距${config.nodesep}，层级间距${config.ranksep}，耗时: ${layoutTime}ms`,
        hasPositions,
        { ...config, layoutTime, hasPositions }
      )
    })

  } catch (error) {
    addResult('❌ 布局间距测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

// 测试大数据量布局
const testLargeDataLayout = () => {
  try {
    addResult('开始大数据量布局测试', '测试不同数据量级的布局性能', true)

    const dataSizes = [50, 100, 200, 500]

    dataSizes.forEach(size => {
      const largeData = createLargeDataset(size)

      const startTime = performance.now()
      const layoutedData = applyDagreLayout(largeData, {
        enableOptimization: true,
        largeDataThreshold: 100
      })
      const endTime = performance.now()

      const layoutTime = Math.round(endTime - startTime)
      const hasPositions = layoutedData.nodes.every(node =>
        typeof node.x === 'number' && typeof node.y === 'number'
      )

      addResult(
        `✅ ${size}节点布局`,
        `${size}个节点布局${hasPositions ? '成功' : '失败'}，耗时: ${layoutTime}ms`,
        hasPositions,
        {
          nodeCount: size,
          layoutTime,
          hasPositions,
          optimized: size > 100
        }
      )
    })

  } catch (error) {
    addResult('❌ 大数据量布局测试失败', error instanceof Error ? error.message : '未知错误', false)
  }
}

// 应用布局配置
const applyLayoutConfig = () => {
  try {
    addResult('应用自定义布局配置', '使用当前配置参数进行布局测试', true)

    const lineageData = createSampleLineageData()
    const g6Data = transformToG6Data(lineageData)

    const startTime = performance.now()
    const layoutedData = applyDagreLayout(g6Data, layoutConfig)
    const endTime = performance.now()

    const layoutTime = Math.round(endTime - startTime)
    const hasPositions = layoutedData.nodes.every(node =>
      typeof node.x === 'number' && typeof node.y === 'number'
    )

    performanceStats.nodeCount = layoutedData.nodes.length
    performanceStats.edgeCount = layoutedData.edges.length
    performanceStats.layoutTime = layoutTime

    addResult(
      hasPositions ? '✅ 自定义配置布局成功' : '❌ 自定义配置布局失败',
      `使用自定义配置布局${hasPositions ? '成功' : '失败'}，耗时: ${layoutTime}ms`,
      hasPositions,
      { ...layoutConfig, layoutTime, hasPositions }
    )

    message.success('布局配置应用成功')

  } catch (error) {
    addResult('❌ 自定义配置布局失败', error instanceof Error ? error.message : '未知错误', false)
    message.error('布局配置应用失败')
  }
}

// 创建大数据量测试数据集
const createLargeDataset = (nodeCount: number): G6GraphData => {
  const nodes = []
  const edges = []

  // 创建节点
  for (let i = 0; i < nodeCount; i++) {
    const tableName = `table_${i}`
    const fields = [
      {
        id: `${tableName}.id`,
        label: 'id',
        tableName,
        fieldName: 'id',
        dataType: { type: 'bigint' },
        description: '主键',
        type: 'field' as const,
        isKey: true
      },
      {
        id: `${tableName}.name`,
        label: 'name',
        tableName,
        fieldName: 'name',
        dataType: { type: 'varchar', length: 255 },
        description: '名称',
        type: 'field' as const
      },
      {
        id: `${tableName}.created_at`,
        label: 'created_at',
        tableName,
        fieldName: 'created_at',
        dataType: { type: 'timestamp' },
        description: '创建时间',
        type: 'field' as const
      }
    ]

    nodes.push({
      id: tableName,
      type: 'table-node',
      tableName,
      fields,
      tableInfo: {
        name: tableName,
        type: 'table' as const,
        fields,
        description: `测试表${i}`,
        position: { x: 0, y: 0 }
      },
      label: tableName
    })
  }

  // 创建边（形成链式结构）
  for (let i = 0; i < nodeCount - 1; i++) {
    const lineageEdge = {
      id: `edge_${i}`,
      source: `table_${i}.id`,
      target: `table_${i + 1}.id`,
      transformType: 'DIRECT' as const,
      confidence: 1.0,
      label: `${i} -> ${i + 1}`
    }

    edges.push({
      id: `edge_${i}`,
      source: `table_${i}`,
      target: `table_${i + 1}`,
      type: 'field-edge',
      sourceField: 'id',
      targetField: 'id',
      sourceFieldId: `table_${i}.id`,
      targetFieldId: `table_${i + 1}.id`,
      transformType: 'DIRECT',
      confidence: 1.0,
      label: `${i} -> ${i + 1}`,
      lineageEdge
    })
  }

  // 添加一些分支连接
  for (let i = 0; i < Math.min(nodeCount / 4, 20); i++) {
    const sourceIndex = Math.floor(Math.random() * (nodeCount - 1))
    const targetIndex = Math.floor(Math.random() * (nodeCount - 1))

    if (sourceIndex !== targetIndex) {
      const lineageEdge = {
        id: `branch_edge_${i}`,
        source: `table_${sourceIndex}.name`,
        target: `table_${targetIndex}.name`,
        transformType: 'TRANSFORM' as const,
        confidence: 0.8,
        label: `branch_${i}`
      }

      edges.push({
        id: `branch_edge_${i}`,
        source: `table_${sourceIndex}`,
        target: `table_${targetIndex}`,
        type: 'field-edge',
        sourceField: 'name',
        targetField: 'name',
        sourceFieldId: `table_${sourceIndex}.name`,
        targetFieldId: `table_${targetIndex}.name`,
        transformType: 'TRANSFORM',
        confidence: 0.8,
        label: `branch_${i}`,
        lineageEdge
      })
    }
  }

  return { nodes, edges }
}
</script>

<style scoped>
.layout-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card,
.config-card,
.stats-card {
  margin-bottom: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
}

.success-title {
  color: #52c41a;
}

.error-title {
  color: #ff4d4f;
}

.test-details {
  margin-top: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
}

.test-details pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
