<template>
  <div class="demo-view">
    <div class="demo-header">
      <h1>血缘图组件演示</h1>
      <p>展示不同业务场景下的数据血缘关系</p>
    </div>

    <div class="demo-controls">
      <a-card title="演示场景选择" class="scenario-card">
        <a-radio-group v-model:value="selectedScenario" @change="handleScenarioChange">
          <a-radio-button value="basic">基础示例</a-radio-button>
          <a-radio-button value="ecommerce">电商场景</a-radio-button>
          <a-radio-button value="finance">金融场景</a-radio-button>
          <a-radio-button value="supply-chain">供应链</a-radio-button>
          <a-radio-button value="analytics">数据分析</a-radio-button>
          <a-radio-button value="complex">复杂查询</a-radio-button>
        </a-radio-group>
      </a-card>

      <a-card title="显示配置" class="config-card">
        <a-space direction="vertical" style="width: 100%">
          <a-switch
            v-model:checked="showFieldLevel"
            @change="handleConfigChange"
          >
            <template #checkedChildren>字段级</template>
            <template #unCheckedChildren>表级</template>
          </a-switch>

          <a-switch
            v-model:checked="showDataTypes"
            @change="handleConfigChange"
          >
            <template #checkedChildren>显示类型</template>
            <template #unCheckedChildren>隐藏类型</template>
          </a-switch>

          <a-select
            v-model:value="layoutDirection"
            @change="handleLayoutChange"
            style="width: 120px"
          >
            <a-select-option value="LR">左到右</a-select-option>
            <a-select-option value="TB">上到下</a-select-option>
            <a-select-option value="RL">右到左</a-select-option>
            <a-select-option value="BT">下到上</a-select-option>
          </a-select>
        </a-space>
      </a-card>
    </div>

    <div class="demo-content">
      <a-row :gutter="16">
        <!-- 左侧：场景描述和SQL -->
        <a-col :span="8">
          <a-card title="场景描述" class="scenario-description">
            <div v-html="currentScenarioDescription"></div>
          </a-card>

          <a-card title="示例SQL" class="sql-display" style="margin-top: 16px">
            <pre class="sql-code">{{ currentSqlText }}</pre>
          </a-card>

          <a-card title="数据统计" class="data-stats" style="margin-top: 16px">
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="表数量">
                {{ dataStatistics.tableCount }}
              </a-descriptions-item>
              <a-descriptions-item label="字段数量">
                {{ dataStatistics.fieldCount }}
              </a-descriptions-item>
              <a-descriptions-item label="关系数量">
                {{ dataStatistics.edgeCount }}
              </a-descriptions-item>
              <a-descriptions-item label="转换类型">
                {{ dataStatistics.transformTypes.join(', ') }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- 右侧：血缘图展示 -->
        <a-col :span="16">
          <a-card title="血缘关系图" class="lineage-display">
            <div class="graph-container">
              <LineageGraph
                ref="graphRef"
                :data="g6GraphData"
                :width="800"
                :height="600"
                @node-click="handleNodeClick"
                @field-click="handleFieldClick"
              />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 字段详情抽屉 -->
    <FieldDetailDrawer
      v-model:visible="showFieldDetail"
      :field-data="selectedFieldData"
      @close="handleFieldDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import LineageGraph from '@/components/LineageGraph.vue'
import FieldDetailDrawer from '@/components/FieldDetailDrawer.vue'
import { generateDemoData, type DemoScenario } from '@/utils/demoDataGenerator'
import { transformToG6Data } from '@/utils/graphDataTransform'
import type { LineageData, G6GraphData, LineageNode } from '@/types/lineage'

// 响应式数据
const selectedScenario = ref<DemoScenario>('basic')
const showFieldLevel = ref(true)
const showDataTypes = ref(true)
const layoutDirection = ref<'LR' | 'TB' | 'RL' | 'BT'>('LR')

const currentLineageData = ref<LineageData | null>(null)
const g6GraphData = ref<G6GraphData | null>(null)
const showFieldDetail = ref(false)
const selectedFieldData = ref<LineageNode | null>(null)

const graphRef = ref()

// 场景描述映射
const scenarioDescriptions: Record<DemoScenario, string> = {
  basic: `
    <h4>基础示例</h4>
    <p>展示最简单的用户-订单关联关系，包含：</p>
    <ul>
      <li>用户表 (users)</li>
      <li>订单表 (orders)</li>
      <li>基础的JOIN关联</li>
    </ul>
    <p>适合初学者理解血缘关系的基本概念。</p>
  `,
  ecommerce: `
    <h4>电商场景</h4>
    <p>完整的电商业务数据模型，包含：</p>
    <ul>
      <li>用户、订单、产品、分类等核心表</li>
      <li>复杂的多表关联关系</li>
      <li>聚合计算和视图创建</li>
    </ul>
    <p>展示真实业务场景中的复杂血缘关系。</p>
  `,
  finance: `
    <h4>金融场景</h4>
    <p>金融业务的风险评估模型，包含：</p>
    <ul>
      <li>客户、账户、交易等金融表</li>
      <li>风险评分计算逻辑</li>
      <li>聚合分析和指标计算</li>
    </ul>
    <p>展示金融行业的数据血缘追踪需求。</p>
  `,
  'supply-chain': `
    <h4>供应链场景</h4>
    <p>供应链管理的数据模型，包含：</p>
    <ul>
      <li>供应商、库存、采购等表</li>
      <li>库存流转和成本计算</li>
      <li>供应链指标分析</li>
    </ul>
    <p>展示供应链数据的复杂转换关系。</p>
  `,
  analytics: `
    <h4>数据分析场景</h4>
    <p>数据分析和报表场景，包含：</p>
    <ul>
      <li>多维度数据分析</li>
      <li>指标计算和聚合</li>
      <li>报表数据血缘追踪</li>
    </ul>
    <p>展示BI和数据分析中的血缘关系。</p>
  `,
  complex: `
    <h4>复杂查询场景</h4>
    <p>包含复杂SQL逻辑的场景，包含：</p>
    <ul>
      <li>CTE和子查询</li>
      <li>窗口函数和分析函数</li>
      <li>复杂的数据转换逻辑</li>
    </ul>
    <p>展示高级SQL查询的血缘解析能力。</p>
  `
}

// 计算属性
const currentScenarioDescription = computed(() => {
  return scenarioDescriptions[selectedScenario.value] || ''
})

const currentSqlText = computed(() => {
  return currentLineageData.value?.metadata?.sqlText || ''
})

const dataStatistics = computed(() => {
  if (!currentLineageData.value) {
    return {
      tableCount: 0,
      fieldCount: 0,
      edgeCount: 0,
      transformTypes: []
    }
  }

  const data = currentLineageData.value
  const transformTypes = [...new Set(data.edges.map(edge => edge.transformType))]

  return {
    tableCount: Object.keys(data.tables).length,
    fieldCount: data.nodes.length,
    edgeCount: data.edges.length,
    transformTypes
  }
})

// 方法
const loadScenarioData = (scenario: DemoScenario) => {
  try {
    const lineageData = generateDemoData(scenario)
    currentLineageData.value = lineageData

    // 转换为G6数据格式
    const g6Data = transformToG6Data(lineageData)

    g6GraphData.value = g6Data
    message.success(`已加载${scenarioDescriptions[scenario].match(/<h4>(.*?)<\/h4>/)?.[1]}数据`)
  } catch (error) {
    console.error('Failed to load scenario data:', error)
    message.error('加载演示数据失败')
  }
}

const handleScenarioChange = () => {
  loadScenarioData(selectedScenario.value)
}

const handleConfigChange = () => {
  if (currentLineageData.value) {
    const g6Data = transformToG6Data(currentLineageData.value)
    g6GraphData.value = g6Data
  }
}

const handleLayoutChange = () => {
  handleConfigChange()
  // 重新应用布局
  if (graphRef.value?.reapplyLayout) {
    graphRef.value.reapplyLayout({
      rankdir: layoutDirection.value
    })
  }
}

const handleNodeClick = (node: any) => {
  console.log('节点点击:', node)
  message.info(`点击了节点: ${node.data?.tableName || node.id}`)
}

const handleFieldClick = (field: any) => {
  console.log('字段点击:', field)
  selectedFieldData.value = field
  showFieldDetail.value = true
}

const handleFieldDetailClose = () => {
  showFieldDetail.value = false
  selectedFieldData.value = null
}

// 生命周期
onMounted(() => {
  loadScenarioData(selectedScenario.value)
})

// 监听器
watch(selectedScenario, (newScenario) => {
  loadScenarioData(newScenario)
})
</script>

<style scoped>
.demo-view {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 24px;
}

.demo-header h1 {
  color: #1890ff;
  margin-bottom: 8px;
}

.demo-header p {
  color: #666;
  font-size: 16px;
}

.demo-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.scenario-card {
  flex: 1;
}

.config-card {
  width: 300px;
}

.demo-content {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.scenario-description {
  height: 200px;
}

.scenario-description :deep(.ant-card-body) {
  height: calc(100% - 57px);
  overflow-y: auto;
}

.sql-display {
  height: 200px;
}

.sql-code {
  background: #f6f8fa;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.data-stats :deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

.lineage-display {
  height: 680px;
}

.graph-container {
  width: 100%;
  height: 600px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}
</style>
