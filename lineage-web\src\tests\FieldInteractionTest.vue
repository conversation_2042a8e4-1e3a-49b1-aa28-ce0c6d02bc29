<template>
  <div class="field-interaction-test">
    <a-card title="字段交互功能测试" style="margin: 20px;">
      <div class="test-controls">
        <a-space>
          <a-button type="primary" @click="loadTestData">
            加载测试数据
          </a-button>
          <a-button @click="clearData">
            清空数据
          </a-button>
          <a-divider type="vertical" />
          <a-tag v-if="hoveredField" color="blue">
            悬浮字段: {{ hoveredField }}
          </a-tag>
          <a-tag v-if="selectedField" color="green">
            选中字段: {{ selectedField }}
          </a-tag>
        </a-space>
      </div>

      <div class="test-graph" style="height: 600px; margin-top: 20px; border: 1px solid #d9d9d9;">
        <LineageGraph
          ref="graphRef"
          :data="testData"
          :width="800"
          :height="600"
          @graph-ready="handleGraphReady"
          @field-hover="handleFieldHover"
          @field-click="handleFieldClick"
          @field-leave="handleFieldLeave"
          @node-click="handleNodeClick"
          @edge-click="handleEdgeClick"
        />
      </div>

      <div class="test-info" style="margin-top: 20px;">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-card size="small" title="悬浮事件">
              <p>当前悬浮字段: {{ hoveredField || '无' }}</p>
              <p>悬浮事件次数: {{ hoverCount }}</p>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card size="small" title="点击事件">
              <p>当前选中字段: {{ selectedField || '无' }}</p>
              <p>点击事件次数: {{ clickCount }}</p>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card size="small" title="Tooltip状态">
              <p>Tooltip显示: {{ tooltipVisible ? '是' : '否' }}</p>
              <p>路径追踪状态: {{ pathTracing ? '开启' : '关闭' }}</p>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <div class="event-log" style="margin-top: 20px;">
        <a-card size="small" title="事件日志">
          <div style="height: 200px; overflow-y: auto;">
            <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
              <a-tag :color="getLogColor(log.type)">{{ log.type }}</a-tag>
              <span>{{ log.message }}</span>
              <small style="color: #999; margin-left: 10px;">{{ log.time }}</small>
            </div>
          </div>
          <a-button size="small" @click="clearLogs" style="margin-top: 10px;">
            清空日志
          </a-button>
        </a-card>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import LineageGraph from '@/components/LineageGraph.vue'
import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data } from '@/utils/graphDataTransform'
import type { G6GraphData } from '@/types/lineage'

// 响应式数据
const graphRef = ref()
const testData = ref<G6GraphData | null>(null)
const hoveredField = ref<string | null>(null)
const selectedField = ref<string | null>(null)
const hoverCount = ref(0)
const clickCount = ref(0)
const tooltipVisible = ref(false)
const pathTracing = ref(false)

// 事件日志
const eventLogs = ref<Array<{
  type: string
  message: string
  time: string
}>>([])

// 加载测试数据
const loadTestData = () => {
  try {
    const sampleData = createSampleLineageData()
    testData.value = transformToG6Data(sampleData)

    addLog('INFO', '测试数据加载成功')
    message.success('测试数据加载成功')
  } catch (error) {
    addLog('ERROR', `数据加载失败: ${error}`)
    message.error('数据加载失败')
  }
}

// 清空数据
const clearData = () => {
  testData.value = null
  hoveredField.value = null
  selectedField.value = null
  tooltipVisible.value = false
  pathTracing.value = false

  addLog('INFO', '数据已清空')
  message.info('数据已清空')
}

// 事件处理方法
const handleGraphReady = (graph: any) => {
  addLog('INFO', '图谱初始化完成')
  console.log('图谱初始化完成:', graph)
}

const handleFieldHover = (fieldId: string, fieldData: any, event: any) => {
  hoveredField.value = fieldId
  hoverCount.value++
  tooltipVisible.value = true

  addLog('HOVER', `悬浮字段: ${fieldData?.fieldName || fieldId}`)
  console.log('字段悬浮:', fieldId, fieldData, event)
}

const handleFieldClick = (fieldId: string, fieldData: any, event: any) => {
  selectedField.value = fieldId
  clickCount.value++

  addLog('CLICK', `点击字段: ${fieldData?.fieldName || fieldId}`)
  console.log('字段点击:', fieldId, fieldData, event)

  // 触发字段高亮
  if (graphRef.value) {
    graphRef.value.setFieldHighlight(fieldId, true)

    // 2秒后清除高亮
    setTimeout(() => {
      if (graphRef.value) {
        graphRef.value.setFieldHighlight(fieldId, false)
      }
    }, 2000)
  }

  // 模拟路径追踪
  pathTracing.value = true
  setTimeout(() => {
    pathTracing.value = false
  }, 2000)
}

const handleFieldLeave = (fieldId: string) => {
  hoveredField.value = null
  tooltipVisible.value = false

  addLog('LEAVE', `离开字段: ${fieldId}`)
  console.log('字段离开:', fieldId)
}

const handleNodeClick = (nodeId: string, nodeData: any) => {
  addLog('NODE', `点击节点: ${nodeId}`)
  console.log('节点点击:', nodeId, nodeData)
}

const handleEdgeClick = (edgeId: string, edgeData: any) => {
  addLog('EDGE', `点击连线: ${edgeId}`)
  console.log('连线点击:', edgeId, edgeData)
}

// 辅助方法
const addLog = (type: string, message: string) => {
  eventLogs.value.unshift({
    type,
    message,
    time: new Date().toLocaleTimeString()
  })

  // 限制日志数量
  if (eventLogs.value.length > 100) {
    eventLogs.value = eventLogs.value.slice(0, 100)
  }
}

const clearLogs = () => {
  eventLogs.value = []
  addLog('INFO', '日志已清空')
}

const getLogColor = (type: string) => {
  const colors: Record<string, string> = {
    'HOVER': 'blue',
    'CLICK': 'green',
    'LEAVE': 'orange',
    'NODE': 'purple',
    'EDGE': 'cyan',
    'INFO': 'default',
    'ERROR': 'red'
  }
  return colors[type] || 'default'
}

// 自动加载测试数据
loadTestData()
</script>

<style scoped>
.field-interaction-test {
  padding: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-graph {
  border-radius: 6px;
  overflow: hidden;
}

.log-item {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
