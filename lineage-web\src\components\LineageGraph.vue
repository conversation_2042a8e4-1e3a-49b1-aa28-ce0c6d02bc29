<template>
  <div id="lineage-graph" class="lineage-graph">
    <!-- G6图谱将在这里渲染 -->
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { Graph } from '@antv/g6'

const data = { "nodes": [{ "id": "0" }, { "id": "1" }, { "id": "2" }, { "id": "3" }, { "id": "4", "combo": "A" }, { "id": "5", "combo": "B" }, { "id": "6", "combo": "A" }, { "id": "7", "combo": "C" }, { "id": "8", "combo": "C" }, { "id": "9", "combo": "A" }, { "id": "10", "combo": "B" }, { "id": "11", "combo": "B" }], "edges": [{ "id": "edge-102", "source": "0", "target": "1" }, { "id": "edge-161", "source": "0", "target": "2" }, { "id": "edge-237", "source": "1", "target": "4" }, { "id": "edge-253", "source": "0", "target": "3" }, { "id": "edge-133", "source": "3", "target": "4" }, { "id": "edge-320", "source": "2", "target": "5" }, { "id": "edge-355", "source": "1", "target": "6" }, { "id": "edge-823", "source": "1", "target": "7" }, { "id": "edge-665", "source": "3", "target": "8" }, { "id": "edge-884", "source": "3", "target": "9" }, { "id": "edge-536", "source": "5", "target": "10" }, { "id": "edge-401", "source": "5", "target": "11" }], "combos": [{ "id": "A", "style": { "type": "rect" } }, { "id": "B", "style": { "type": "rect" } }, { "id": "C", "style": { "type": "rect" } }] };

// 页面加载时直接执行核心渲染逻辑
onMounted(() => {
  // fetch('https://assets.antv.antgroup.com/g6/dagre-combo.json')
  //   .then((res) => res.json())
  //   .then((data) => {
      const graph = new Graph({
        container: 'lineage-graph',
        autoFit: 'center',
        data,
        node: {
          type: 'rect',
          style: {
            size: [60, 30],
            radius: 8,
            labelText: (d) => d.id,
            labelBackground: true,
            ports: [{ placement: 'top' }, { placement: 'bottom' }],
          },
          palette: {
            field: (d) => d.combo,
          },
        },
        edge: {
          type: 'cubic-vertical',
          style: {
            endArrow: true,
          },
        },
        combo: {
          type: 'rect',
          style: {
            radius: 8,
            labelText: (d) => d.id,
          },
        },
        layout: {
          type: 'antv-dagre',
          ranksep: 50,
          nodesep: 5,
          sortByCombo: true,
        },
        behaviors: ['drag-element', 'drag-canvas', 'zoom-canvas'],
      });

      graph.render();
    // });
});
</script>

<style scoped>
.lineage-graph {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.04);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.02);
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .lineage-graph {
    background: linear-gradient(135deg, #1f1f1f 0%, #141414 100%);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
  }
}
</style>
