<template>
  <div class="centering-debug">
    <h1>居中调试页面</h1>
    
    <div class="debug-section">
      <h2>基础居中测试</h2>
      <div class="debug-container">
        <div class="debug-content">
          <div class="debug-item">
            <div class="debug-icon">📦</div>
            <h3>数据血缘图谱</h3>
            <p>请在左侧输入 SQL 语句并点击『解析血缘』按钮开始分析</p>
            <div class="debug-actions">
              <button class="debug-btn primary">加载示例数据</button>
              <button class="debug-btn">查看帮助</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h2>使用 LoadingState 组件</h2>
      <div class="debug-container">
        <div class="graph-content">
          <div class="graph-container">
            <LoadingState
              :empty="true"
              empty-title="数据血缘图谱"
              empty-message="请在左侧输入 SQL 语句并点击『解析血缘』按钮开始分析"
              :empty-actions="emptyActions"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h2>CSS Grid 居中测试</h2>
      <div class="debug-container grid">
        <div class="debug-item">
          <div class="debug-icon">📦</div>
          <h3>CSS Grid 居中</h3>
          <p>使用 CSS Grid 实现的居中效果</p>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h2>Flexbox 居中测试</h2>
      <div class="debug-container flex">
        <div class="debug-item">
          <div class="debug-icon">📦</div>
          <h3>Flexbox 居中</h3>
          <p>使用 Flexbox 实现的居中效果</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LoadingState from '@/components/LoadingState.vue'

const emptyActions = ref([
  {
    key: 'sample',
    text: '加载示例数据',
    type: 'primary' as const,
    icon: 'ShareAltOutlined',
    handler: () => console.log('加载示例数据')
  },
  {
    key: 'help',
    text: '查看帮助',
    type: 'default' as const,
    icon: 'QuestionCircleOutlined',
    handler: () => console.log('查看帮助')
  }
])
</script>

<style scoped>
.centering-debug {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 48px;
}

.debug-section h1 {
  color: #1890ff;
  text-align: center;
  margin-bottom: 32px;
}

.debug-section h2 {
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.debug-container {
  width: 100%;
  height: 400px;
  border: 3px solid #1890ff;
  border-radius: 8px;
  margin-bottom: 24px;
  position: relative;
  background: #f0f8ff;
  /* 网格背景 */
  background-image: 
    linear-gradient(rgba(24,144,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(24,144,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 基础 flexbox 居中 */
.debug-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* CSS Grid 居中 */
.debug-container.grid {
  display: grid;
  place-items: center;
}

/* Flexbox 居中 */
.debug-container.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-item {
  background: white;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
  width: auto;
  border: 2px solid #52c41a;
}

.debug-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.debug-item h3 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.debug-item p {
  margin: 0 0 20px 0;
  color: #666;
  line-height: 1.5;
}

.debug-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
}

.debug-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.debug-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.debug-btn.primary {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.debug-btn.primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 复制 LineageLayout.vue 中的样式 */
.graph-content {
  padding: 0;
  background: var(--color-background, #ffffff);
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
}

.graph-container {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--color-background, #ffffff);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
