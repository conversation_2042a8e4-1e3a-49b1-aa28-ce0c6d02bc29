# 组件使用文档

本文档详细介绍了字段级别数据血缘图组件的使用方法、API接口和最佳实践。

## 目录

- [核心组件](#核心组件)
  - [LineageGraph](#lineagegraph)
  - [LineageLayout](#lineagelayout)
  - [SqlEditor](#sqleditor)
- [工具组件](#工具组件)
  - [ErrorBoundary](#errorboundary)
  - [LoadingState](#loadingstate)
  - [FieldDetailDrawer](#fielddetaildrawer)
- [状态管理](#状态管理)
  - [useLineageStore](#uselineagestore)
- [工具函数](#工具函数)
  - [数据转换](#数据转换)
  - [错误处理](#错误处理)

## 核心组件

### LineageGraph

血缘图的核心渲染组件，基于 AntV G6 实现。

#### Props

```typescript
interface LineageGraphProps {
  data: G6GraphData           // 图数据
  width?: number              // 画布宽度
  height?: number             // 画布高度
  config?: GraphConfig        // 图配置
  theme?: 'light' | 'dark'    // 主题
}
```

#### Events

```typescript
interface LineageGraphEvents {
  'graph-ready': (graph: Graph) => void           // 图初始化完成
  'node-click': (node: NodeConfig) => void        // 节点点击
  'edge-click': (edge: EdgeConfig) => void        // 边点击
  'canvas-click': () => void                      // 画布点击
  'field-hover': (field: FieldInfo) => void       // 字段悬停
  'field-click': (field: FieldInfo) => void       // 字段点击
  'field-leave': () => void                       // 字段离开
}
```

#### 使用示例

```vue
<template>
  <LineageGraph
    :data="graphData"
    :width="800"
    :height="600"
    :config="graphConfig"
    theme="light"
    @graph-ready="handleGraphReady"
    @node-click="handleNodeClick"
    @field-click="handleFieldClick"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LineageGraph from '@/components/LineageGraph.vue'
import type { G6GraphData, GraphConfig } from '@/types/lineage'

const graphData = ref<G6GraphData>({
  nodes: [
    {
      id: 'table1',
      type: 'table-node',
      label: '用户表',
      data: {
        tableName: 'users',
        fields: [
          { id: 'users.id', fieldName: 'id', dataType: { type: 'INT' } },
          { id: 'users.name', fieldName: 'name', dataType: { type: 'VARCHAR' } }
        ]
      }
    }
  ],
  edges: []
})

const graphConfig = ref<GraphConfig>({
  layout: {
    type: 'dagre',
    rankdir: 'LR',
    nodesep: 50,
    ranksep: 100
  },
  node: {
    size: [200, 120],
    style: {
      fill: '#f0f0f0',
      stroke: '#d9d9d9'
    }
  }
})

const handleGraphReady = (graph: any) => {
  console.log('图初始化完成', graph)
}

const handleNodeClick = (node: any) => {
  console.log('节点点击', node)
}

const handleFieldClick = (field: any) => {
  console.log('字段点击', field)
}
</script>
```

#### 最佳实践

1. **性能优化**
   - 大数据量时使用虚拟化渲染
   - 合理设置节点和边的样式复杂度
   - 使用防抖处理频繁的交互事件

2. **交互设计**
   - 提供清晰的视觉反馈
   - 支持键盘快捷键操作
   - 实现合理的缩放和平移限制

3. **数据处理**
   - 在传入数据前进行验证和清理
   - 使用合适的布局算法
   - 处理空数据和异常情况

### LineageLayout

血缘图的主布局组件，包含SQL编辑器、图谱展示和配置面板。

#### Props

```typescript
interface LineageLayoutProps {
  initialSql?: string         // 初始SQL
  showSqlEditor?: boolean     // 是否显示SQL编辑器
  showConfigPanel?: boolean   // 是否显示配置面板
  readonly?: boolean          // 是否只读模式
}
```

#### 使用示例

```vue
<template>
  <LineageLayout
    :initial-sql="sqlText"
    :show-sql-editor="true"
    :show-config-panel="true"
    :readonly="false"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LineageLayout from '@/components/LineageLayout.vue'

const sqlText = ref(`
SELECT 
  u.id,
  u.name,
  o.order_date
FROM users u
JOIN orders o ON u.id = o.user_id
WHERE u.status = 'active'
`)
</script>
```

### SqlEditor

SQL代码编辑器组件，基于 CodeMirror 实现。

#### Props

```typescript
interface SqlEditorProps {
  modelValue: string          // SQL文本
  placeholder?: string        // 占位符
  readonly?: boolean          // 是否只读
  height?: string            // 编辑器高度
  theme?: 'light' | 'dark'   // 主题
  showLineNumbers?: boolean   // 是否显示行号
  enableAutoComplete?: boolean // 是否启用自动补全
}
```

#### Events

```typescript
interface SqlEditorEvents {
  'update:modelValue': (value: string) => void   // 内容更新
  'format': () => void                           // 格式化
  'execute': () => void                          // 执行
}
```

#### 使用示例

```vue
<template>
  <SqlEditor
    v-model="sqlText"
    placeholder="请输入SQL语句..."
    :height="'300px'"
    theme="light"
    :show-line-numbers="true"
    :enable-auto-complete="true"
    @format="handleFormat"
    @execute="handleExecute"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SqlEditor from '@/components/SqlEditor.vue'

const sqlText = ref('')

const handleFormat = () => {
  console.log('格式化SQL')
}

const handleExecute = () => {
  console.log('执行SQL', sqlText.value)
}
</script>
```

## 工具组件

### ErrorBoundary

错误边界组件，用于捕获和处理组件错误。

#### Props

```typescript
interface ErrorBoundaryProps {
  fallbackTitle?: string      // 错误标题
  fallbackMessage?: string    // 错误消息
  showDetails?: boolean       // 是否显示错误详情
  enableErrorReporting?: boolean // 是否启用错误报告
  onError?: (error: Error, errorInfo: any) => void // 错误回调
  onRetry?: () => void        // 重试回调
}
```

#### 使用示例

```vue
<template>
  <ErrorBoundary
    fallback-title="组件加载失败"
    fallback-message="请尝试刷新页面或联系技术支持"
    :show-details="isDev"
    :enable-error-reporting="true"
    @error="handleError"
    @retry="handleRetry"
  >
    <YourComponent />
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

const isDev = computed(() => import.meta.env.DEV)

const handleError = (error: Error, errorInfo: any) => {
  console.error('组件错误:', error, errorInfo)
  // 发送错误报告到监控服务
}

const handleRetry = () => {
  console.log('重试操作')
  // 重新加载组件或数据
}
</script>
```

### LoadingState

统一的加载状态组件，支持多种展示模式。

#### Props

```typescript
interface LoadingStateProps {
  // 状态控制
  loading?: boolean
  error?: boolean
  empty?: boolean
  success?: boolean
  overlay?: boolean

  // 加载状态
  loadingText?: string
  spinnerType?: 'default' | 'sync' | 'reload' | 'download'
  spinSize?: 'small' | 'default' | 'large'
  
  // 进度相关
  showProgress?: boolean
  progress?: number
  progressStatus?: 'normal' | 'exception' | 'active' | 'success'
  progressText?: string
  
  // 加载详情
  loadingDetails?: boolean
  loadingSteps?: LoadingStep[]
  showCancel?: boolean

  // 错误状态
  errorTitle?: string
  errorMessage?: string
  errorDetails?: string
  showRetry?: boolean
  showReport?: boolean

  // 空状态
  emptyTitle?: string
  emptyMessage?: string
  emptyActions?: ActionButton[]
}
```

#### 使用示例

```vue
<template>
  <LoadingState
    :loading="isLoading"
    loading-text="正在加载数据..."
    :show-progress="true"
    :progress="loadingProgress"
    :loading-steps="steps"
    
    :error="hasError"
    error-title="加载失败"
    :error-message="errorMessage"
    :show-retry="true"
    
    :empty="isEmpty"
    empty-title="暂无数据"
    empty-message="请添加数据或调整筛选条件"
    :empty-actions="emptyActions"
    
    @retry="handleRetry"
    @cancel="handleCancel"
    @empty-action="handleEmptyAction"
  >
    <YourContent v-if="!isLoading && !hasError && !isEmpty" />
  </LoadingState>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LoadingState from '@/components/LoadingState.vue'

const isLoading = ref(false)
const hasError = ref(false)
const isEmpty = ref(false)
const loadingProgress = ref(0)
const errorMessage = ref('')

const steps = ref([
  { text: '初始化', status: 'completed' },
  { text: '加载数据', status: 'active' },
  { text: '渲染界面', status: 'pending' }
])

const emptyActions = ref([
  { key: 'add', text: '添加数据', type: 'primary' },
  { key: 'refresh', text: '刷新', type: 'default' }
])

const handleRetry = () => {
  console.log('重试加载')
}

const handleCancel = () => {
  console.log('取消加载')
}

const handleEmptyAction = (action: any) => {
  console.log('空状态操作:', action)
}
</script>
```

### FieldDetailDrawer

字段详情抽屉组件，用于展示字段的详细信息。

#### Props

```typescript
interface FieldDetailDrawerProps {
  visible: boolean            // 是否显示
  field?: LineageNode        // 字段信息
  width?: number             // 抽屉宽度
  placement?: 'left' | 'right' | 'top' | 'bottom' // 抽屉位置
}
```

#### 使用示例

```vue
<template>
  <FieldDetailDrawer
    :visible="showDrawer"
    :field="selectedField"
    :width="400"
    placement="right"
    @close="handleCloseDrawer"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FieldDetailDrawer from '@/components/FieldDetailDrawer.vue'
import type { LineageNode } from '@/types/lineage'

const showDrawer = ref(false)
const selectedField = ref<LineageNode | undefined>()

const handleCloseDrawer = () => {
  showDrawer.value = false
  selectedField.value = undefined
}
</script>
```

## 状态管理

### useLineageStore

血缘图的状态管理 Store，基于 Pinia 实现。

#### 状态

```typescript
interface LineageStoreState {
  // 数据状态
  lineageData: LineageData | null
  g6GraphData: G6GraphData | null
  sqlText: string
  databaseType: DatabaseType

  // 加载状态
  loading: boolean
  loadingText: string
  loadingProgress?: number
  loadingSteps: LoadingStep[]

  // 错误状态
  error: string | null
  errorDetails: any
  errorType: string
  retryCount: number
  maxRetries: number

  // 图配置
  graphConfig: GraphConfig
  showFieldLevelLineage: boolean
  showCompleteLineage: boolean

  // 交互状态
  selectedNodes: Set<string>
  selectedEdges: Set<string>
  hoveredNode: string | null
  searchKeyword: string
  theme: 'light' | 'dark'
  fieldFilterConfig: FieldFilterConfig
}
```

#### 方法

```typescript
interface LineageStoreMethods {
  // 数据操作
  setLineageData(data: LineageData): void
  parseSqlLineage(sql: string): Promise<void>
  loadSampleData(): void
  clearData(): void

  // 状态管理
  setLoading(loading: boolean, text?: string, progress?: number): void
  setError(message: string, details?: any, type?: string): void
  clearError(): void
  retryLastOperation(): Promise<void>

  // 配置管理
  setGraphConfig(config: Partial<GraphConfig>): void
  toggleFieldLevelLineage(): void
  toggleCompleteLineage(): void

  // 交互操作
  selectNode(nodeId: string): void
  selectEdge(edgeId: string): void
  clearSelection(): void
  setHoveredNode(nodeId: string | null): void
  setSearchKeyword(keyword: string): void
  setTheme(theme: 'light' | 'dark'): void

  // 过滤操作
  setFieldFilter(config: FieldFilterConfig): void
  applyFieldFilter(): void
  clearFieldFilter(): void
}
```

#### 使用示例

```vue
<template>
  <div>
    <button @click="loadData">加载数据</button>
    <button @click="toggleTheme">切换主题</button>

    <div v-if="lineageStore.loading">
      加载中: {{ lineageStore.loadingText }}
    </div>

    <div v-if="lineageStore.error">
      错误: {{ lineageStore.error }}
      <button @click="lineageStore.retryLastOperation">重试</button>
    </div>

    <LineageGraph
      v-if="lineageStore.g6GraphData"
      :data="lineageStore.g6GraphData"
      :theme="lineageStore.theme"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script setup lang="ts">
import { useLineageStore } from '@/stores/lineageStore'
import LineageGraph from '@/components/LineageGraph.vue'

const lineageStore = useLineageStore()

const loadData = async () => {
  const sql = `
    SELECT u.id, u.name, o.total
    FROM users u
    JOIN orders o ON u.id = o.user_id
  `
  await lineageStore.parseSqlLineage(sql)
}

const toggleTheme = () => {
  const newTheme = lineageStore.theme === 'light' ? 'dark' : 'light'
  lineageStore.setTheme(newTheme)
}

const handleNodeClick = (node: any) => {
  lineageStore.selectNode(node.id)
}
</script>
```

## 工具函数

### 数据转换

#### transformToG6Data

将血缘数据转换为 G6 图数据格式。

```typescript
function transformToG6Data(lineageData: LineageData): G6GraphData

// 使用示例
import { transformToG6Data } from '@/utils/graphDataTransform'

const lineageData: LineageData = {
  tables: { /* ... */ },
  nodes: [ /* ... */ ],
  edges: [ /* ... */ ]
}

const g6Data = transformToG6Data(lineageData)
```

#### validateLineageData

验证血缘数据的完整性和正确性。

```typescript
function validateLineageData(data: any): ValidationResult

// 使用示例
import { validateLineageData } from '@/utils/graphDataTransform'

const result = validateLineageData(rawData)
if (!result.isValid) {
  console.error('数据验证失败:', result.errors)
  console.warn('警告信息:', result.warnings)
}
```

#### sanitizeLineageData

清理和修复血缘数据。

```typescript
function sanitizeLineageData(rawData: any): {
  data: LineageData | null
  report: {
    fixed: string[]
    removed: string[]
    warnings: string[]
  }
}

// 使用示例
import { sanitizeLineageData } from '@/utils/graphDataTransform'

const { data, report } = sanitizeLineageData(rawData)
if (data) {
  console.log('数据已清理:', report)
  // 使用清理后的数据
} else {
  console.error('数据无法修复:', report.warnings)
}
```

### 错误处理

#### errorManager

全局错误管理器，提供统一的错误处理功能。

```typescript
// 基本用法
import { handleError, ErrorType } from '@/utils/errorManager'

try {
  // 可能出错的操作
  await riskyOperation()
} catch (error) {
  handleError(error, ErrorType.NETWORK, {
    showNotification: true,
    retryable: true,
    context: { operation: 'loadData' }
  })
}

// 便捷方法
import {
  handleNetworkError,
  handleValidationError,
  handleBusinessError
} from '@/utils/errorManager'

// 网络错误
handleNetworkError(error, { retryable: true })

// 验证错误
handleValidationError(error, { showMessage: true })

// 业务错误
handleBusinessError(error, { showNotification: true })
```

#### 重试机制

```typescript
import { errorManager } from '@/utils/errorManager'

// 带重试的异步操作
const result = await errorManager.withRetry(
  async () => {
    return await apiCall()
  },
  {
    maxRetries: 3,
    delay: 1000,
    backoff: true  // 指数退避
  }
)
```

## 最佳实践

### 1. 组件组合

```vue
<template>
  <ErrorBoundary @error="handleError">
    <LoadingState
      :loading="loading"
      :error="!!error"
      :empty="isEmpty"
      @retry="handleRetry"
    >
      <LineageLayout
        :initial-sql="sqlText"
        :readonly="readonly"
      />
    </LoadingState>
  </ErrorBoundary>
</template>
```

### 2. 数据流管理

```typescript
// 推荐的数据流
const loadLineageData = async (sql: string) => {
  try {
    // 1. 设置加载状态
    lineageStore.setLoading(true, '正在解析SQL...')

    // 2. 验证输入
    if (!sql.trim()) {
      throw new Error('SQL不能为空')
    }

    // 3. 调用API
    const rawData = await apiClient.parseSql(sql)

    // 4. 验证和清理数据
    const validation = validateLineageData(rawData)
    if (!validation.isValid) {
      const { data } = sanitizeLineageData(rawData)
      if (!data) {
        throw new Error('数据格式错误')
      }
      rawData = data
    }

    // 5. 更新状态
    lineageStore.setLineageData(rawData)

  } catch (error) {
    handleError(error, ErrorType.BUSINESS, {
      showNotification: true,
      retryable: true
    })
  } finally {
    lineageStore.setLoading(false)
  }
}
```

### 3. 性能优化

```typescript
// 使用防抖处理搜索
import { debounce } from '@/utils/graphDataTransform'

const debouncedSearch = debounce((keyword: string) => {
  lineageStore.setSearchKeyword(keyword)
}, 300)

// 大数据量时的优化
const optimizeForLargeData = (data: G6GraphData) => {
  if (data.nodes.length > 1000) {
    // 启用虚拟化
    graphConfig.value.enableVirtualization = true
    // 简化样式
    graphConfig.value.node.style = { /* 简化样式 */ }
  }
}
```

### 4. 错误处理策略

```typescript
// 分层错误处理
const errorHandlingStrategy = {
  // 组件级错误 - 使用ErrorBoundary
  componentError: (error: Error) => {
    // 记录错误，显示降级UI
  },

  // 业务逻辑错误 - 使用errorManager
  businessError: (error: Error) => {
    handleBusinessError(error, {
      showNotification: true,
      retryable: true
    })
  },

  // 网络错误 - 自动重试
  networkError: (error: Error) => {
    handleNetworkError(error, {
      retryable: true,
      maxRetries: 3
    })
  }
}
```
