# G6示例数据更新说明

## 概述

根据提供的G6 dagre-combo代码示例，我们对"加载示例数据"功能进行了全面更新，使其生成符合G6最佳实践的多层数据架构血缘示例。

## 主要更新内容

### 1. 新增G6风格示例数据生成器

**文件**: `src/utils/g6SampleData.ts`

- 创建了专门的G6示例数据生成器
- 模拟真实的多层数据架构：源系统 → 暂存层 → 数据集市
- 支持combo（组合）功能，将表按数据层级分组
- 包含完整的ETL流程示例

**数据层级**:
- `source_layer`: 源数据层（customer_source, order_source, product_source）
- `staging_layer`: 暂存层（dim_customer, fact_order）
- `mart_layer`: 数据集市层（customer_analytics）

### 2. 更新类型定义

**文件**: `src/types/lineage.ts`

- 为 `TableInfo` 接口添加 `combo` 属性
- 为 `LineageEdge` 接口添加 `description` 属性
- 新增 `G6ComboData` 接口
- 更新 `G6GraphData` 接口支持 `combos` 数组
- 为 `G6NodeData` 接口添加 `comboId` 属性

### 3. 增强图数据转换功能

**文件**: `src/utils/graphDataTransform.ts`

- 更新 `transformToG6Data` 函数支持combo功能
- 自动生成combo数据结构
- 为节点设置 `comboId` 属性
- 新增combo相关辅助函数：
  - `getComboLabel()`: 获取combo显示标签
  - `getComboColor()`: 获取combo背景色
  - `getComboStrokeColor()`: 获取combo边框色

### 4. 更新示例数据加载逻辑

**文件**: `src/utils/sqlParser.ts`

- 重构 `createSampleLineageData()` 函数使用新的G6示例数据
- 保留 `createLegacySampleLineageData()` 函数用于向后兼容
- 支持自定义SQL语句覆盖

### 5. 增强测试和调试功能

**文件**: `src/components/LineageLayout.vue`

- 增强示例数据加载后的调试输出
- 添加combo数据检查
- 显示分层信息和转换类型统计

**新增测试页面**: `src/views/G6SampleTest.vue`

- 专门的G6示例数据测试界面
- 支持加载不同类型的示例数据
- 实时显示数据结构分析
- 详细的测试结果展示

## 新功能特性

### 1. 多层数据架构支持

```typescript
// 表的combo属性示例
{
  name: 'customer_source',
  combo: 'source_layer',  // 所属数据层
  schema: 'source',
  database: 'lineage_demo'
}
```

### 2. 丰富的转换类型

- `DIRECT`: 直接映射
- `TRANSFORM`: 数据转换（清洗、格式化）
- `AGGREGATE`: 聚合计算（SUM, COUNT, AVG）
- `JOIN`: 表关联
- `CALCULATION`: 复杂计算

### 3. 完整的血缘关系

示例数据包含17条血缘边，展示完整的数据流转过程：
- 源系统到暂存层的ETL过程
- 暂存层到数据集市的聚合分析
- 包含SQL表达式和置信度信息

### 4. Combo可视化支持

- 自动按数据层级分组显示
- 不同层级使用不同颜色主题
- 支持层级标签本地化

## 使用方法

### 1. 基本使用

```typescript
import { createSampleLineageData } from '@/utils/sqlParser'
import { transformToG6Data } from '@/utils/graphDataTransform'

// 生成示例数据
const lineageData = createSampleLineageData()

// 转换为G6格式
const g6Data = transformToG6Data(lineageData)

// g6Data 现在包含 nodes, edges, combos
```

### 2. 在组件中使用

```vue
<script setup>
import { useLineageStore } from '@/stores/lineageStore'

const lineageStore = useLineageStore()

// 加载示例数据
await lineageStore.loadSampleData()
</script>
```

### 3. 测试页面访问

访问 `http://localhost:5175/g6-sample-test` 查看详细的测试界面。

## 数据结构示例

### Combo数据结构

```typescript
{
  id: 'source_layer',
  label: '源数据层',
  type: 'rect',
  style: {
    radius: 8,
    fill: '#e6f7ff',
    stroke: '#1890ff',
    lineWidth: 2,
    opacity: 0.1
  }
}
```

### 节点数据结构

```typescript
{
  id: 'customer_source',
  type: 'rect',
  comboId: 'source_layer',  // 所属combo
  tableName: 'customer_source',
  fields: [...],
  tableInfo: {...}
}
```

### 边数据结构

```typescript
{
  id: 'edge_1',
  source: 'customer_source',
  target: 'dim_customer',
  type: 'field-edge',
  lineageEdge: {
    transformType: 'TRANSFORM',
    expression: 'TRIM(UPPER(name))',
    confidence: 0.95,
    description: '数据清洗'
  }
}
```

## 兼容性说明

- 保持与现有API的完全兼容
- 原有的 `createSampleLineageData()` 函数现在返回新的G6风格数据
- 提供 `createLegacySampleLineageData()` 函数用于向后兼容
- 所有现有组件无需修改即可使用新数据

## 测试验证

1. **功能测试**: 访问 `/g6-sample-test` 页面进行交互式测试
2. **数据完整性**: 自动验证节点、边、combo数据的一致性
3. **性能测试**: 支持大数据量场景的性能验证
4. **兼容性测试**: 确保与现有功能的兼容性

## 后续优化建议

1. **动态数据源**: 支持从配置文件或API加载示例数据
2. **更多场景**: 添加更多行业场景的示例数据
3. **可视化增强**: 支持更丰富的combo样式和动画效果
4. **国际化**: 支持多语言的combo标签和描述

## 总结

本次更新将"加载示例数据"功能从简单的表关系展示升级为完整的多层数据架构血缘示例，更好地展示了G6图谱的强大功能，为用户提供了更真实、更有价值的数据血缘可视化体验。
