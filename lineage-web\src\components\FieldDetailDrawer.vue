<template>
  <a-drawer
    v-model:open="visible"
    title="字段详情"
    placement="right"
    :width="480"
    :closable="true"
    :mask-closable="true"
    class="field-detail-drawer"
  >
    <div class="field-detail-content" v-if="fieldData">
      <!-- 字段基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <DatabaseOutlined />
          基本信息
        </h3>
        <div class="field-info-card">
          <div class="field-header">
            <div class="field-name">
              <span class="name">{{ fieldData.fieldName }}</span>
              <a-tag v-if="fieldData.isKey" color="gold">主键</a-tag>
              <a-tag v-if="!fieldData.isNullable" color="red">非空</a-tag>
            </div>
            <div class="field-id">{{ fieldData.id }}</div>
          </div>
          
          <a-descriptions :column="1" size="small" bordered>
            <a-descriptions-item label="所属表">
              <a-tag color="blue">{{ fieldData.tableName }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="数据类型" v-if="fieldData.dataType">
              <a-tag :color="getTypeColor(fieldData.dataType.type)">
                {{ formatDataType(fieldData.dataType) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="可为空">
              <a-tag :color="fieldData.isNullable ? 'orange' : 'green'">
                {{ fieldData.isNullable ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="默认值" v-if="fieldData.defaultValue">
              <code>{{ fieldData.defaultValue }}</code>
            </a-descriptions-item>
            <a-descriptions-item label="描述" v-if="fieldData.description">
              {{ fieldData.description }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>

      <!-- 血缘关系 -->
      <div class="detail-section">
        <h3 class="section-title">
          <ShareAltOutlined />
          血缘关系
        </h3>
        <div class="lineage-info">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-statistic
                title="上游字段"
                :value="upstreamFields.length"
                :value-style="{ color: '#1890ff' }"
                prefix="⬆"
              />
            </a-col>
            <a-col :span="12">
              <a-statistic
                title="下游字段"
                :value="downstreamFields.length"
                :value-style="{ color: '#52c41a' }"
                prefix="⬇"
              />
            </a-col>
          </a-row>
        </div>

        <!-- 上游字段列表 -->
        <div class="lineage-list" v-if="upstreamFields.length > 0">
          <h4>上游字段</h4>
          <div class="field-list">
            <div
              v-for="field in upstreamFields"
              :key="field.id"
              class="field-item upstream"
              @click="handleFieldClick(field)"
            >
              <div class="field-info">
                <span class="field-name">{{ field.fieldName }}</span>
                <span class="table-name">{{ field.tableName }}</span>
              </div>
              <div class="transform-type">
                <a-tag size="small" :color="getTransformColor(field.transformType)">
                  {{ field.transformType || 'DIRECT' }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 下游字段列表 -->
        <div class="lineage-list" v-if="downstreamFields.length > 0">
          <h4>下游字段</h4>
          <div class="field-list">
            <div
              v-for="field in downstreamFields"
              :key="field.id"
              class="field-item downstream"
              @click="handleFieldClick(field)"
            >
              <div class="field-info">
                <span class="field-name">{{ field.fieldName }}</span>
                <span class="table-name">{{ field.tableName }}</span>
              </div>
              <div class="transform-type">
                <a-tag size="small" :color="getTransformColor(field.transformType)">
                  {{ field.transformType || 'DIRECT' }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- SQL表达式 -->
      <div class="detail-section" v-if="sqlExpressions.length > 0">
        <h3 class="section-title">
          <CodeOutlined />
          相关SQL表达式
        </h3>
        <div class="sql-expressions">
          <div
            v-for="(expr, index) in sqlExpressions"
            :key="index"
            class="sql-expression"
          >
            <div class="expr-header">
              <span class="expr-type">{{ expr.type }}</span>
              <a-tag size="small">{{ expr.confidence }}% 置信度</a-tag>
            </div>
            <div class="expr-content">
              <pre><code>{{ expr.expression }}</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="detail-actions">
        <a-space>
          <a-button type="primary" @click="handleTraceLineage">
            <ShareAltOutlined />
            追踪血缘路径
          </a-button>
          <a-button @click="handleHighlightField">
            <HighlightOutlined />
            高亮字段
          </a-button>
          <a-button @click="handleExportInfo">
            <ExportOutlined />
            导出信息
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty description="请选择一个字段查看详情" />
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  DatabaseOutlined,
  ShareAltOutlined,
  CodeOutlined,
  HighlightOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'
import type { LineageNode } from '@/types/lineage'
import { getFieldTypeColor, formatDataType } from '@/utils/graphDataTransform'

// Props
interface Props {
  visible?: boolean
  fieldData?: LineageNode | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  fieldData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'trace-lineage': [fieldData: LineageNode]
  'highlight-field': [fieldData: LineageNode]
  'field-click': [fieldData: LineageNode]
}>()

// 响应式数据
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 模拟血缘关系数据（实际应该从store或API获取）
const upstreamFields = computed(() => {
  if (!props.fieldData) return []
  
  // 模拟上游字段数据
  return [
    {
      id: 'users.user_id',
      fieldName: 'user_id',
      tableName: 'users',
      transformType: 'DIRECT'
    },
    {
      id: 'orders.customer_id',
      fieldName: 'customer_id', 
      tableName: 'orders',
      transformType: 'JOIN'
    }
  ]
})

const downstreamFields = computed(() => {
  if (!props.fieldData) return []
  
  // 模拟下游字段数据
  return [
    {
      id: 'user_stats.total_orders',
      fieldName: 'total_orders',
      tableName: 'user_stats',
      transformType: 'AGGREGATE'
    }
  ]
})

// 模拟SQL表达式数据
const sqlExpressions = computed(() => {
  if (!props.fieldData) return []
  
  return [
    {
      type: '字段映射',
      expression: `SELECT ${props.fieldData.fieldName}\nFROM ${props.fieldData.tableName}`,
      confidence: 95
    },
    {
      type: '聚合计算',
      expression: `COUNT(${props.fieldData.fieldName}) as total_count`,
      confidence: 88
    }
  ]
})

// 方法
const getTypeColor = (dataType: string) => {
  return getFieldTypeColor(dataType)
}

const getTransformColor = (transformType?: string) => {
  const colorMap: Record<string, string> = {
    'DIRECT': 'blue',
    'JOIN': 'green',
    'AGGREGATE': 'orange',
    'FILTER': 'purple',
    'TRANSFORM': 'pink',
    'UNION': 'cyan',
    'WINDOW': 'yellow'
  }
  return colorMap[transformType || 'DIRECT'] || 'default'
}

const handleFieldClick = (field: any) => {
  console.log('点击相关字段:', field)
  // 这里可以切换到点击的字段详情
  message.info(`切换到字段: ${field.fieldName}`)
}

const handleTraceLineage = () => {
  if (props.fieldData) {
    emit('trace-lineage', props.fieldData)
    message.success('开始追踪血缘路径')
  }
}

const handleHighlightField = () => {
  if (props.fieldData) {
    emit('highlight-field', props.fieldData)
    message.success('字段已高亮')
  }
}

const handleExportInfo = () => {
  if (props.fieldData) {
    // 模拟导出功能
    const info = {
      fieldName: props.fieldData.fieldName,
      tableName: props.fieldData.tableName,
      dataType: props.fieldData.dataType,
      description: props.fieldData.description,
      upstreamCount: upstreamFields.value.length,
      downstreamCount: downstreamFields.value.length
    }
    
    console.log('导出字段信息:', info)
    message.success('字段信息已导出到控制台')
  }
}
</script>

<style scoped>
.field-detail-content {
  padding: 0;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.field-info-card {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.field-header {
  margin-bottom: 16px;
}

.field-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.field-name .name {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.field-id {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.lineage-info {
  margin-bottom: 16px;
}

.lineage-list {
  margin-bottom: 16px;
}

.lineage-list h4 {
  font-size: 14px;
  font-weight: 600;
  color: #595959;
  margin-bottom: 8px;
}

.field-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.field-item:hover {
  background: #f0f0f0;
}

.field-item.upstream {
  border-left: 3px solid #1890ff;
  background: #f6ffed;
}

.field-item.downstream {
  border-left: 3px solid #52c41a;
  background: #fff7e6;
}

.field-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.field-info .field-name {
  font-weight: 500;
  color: #262626;
}

.field-info .table-name {
  font-size: 12px;
  color: #8c8c8c;
}

.sql-expressions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sql-expression {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.expr-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}

.expr-type {
  font-weight: 500;
  color: #262626;
}

.expr-content {
  padding: 12px;
}

.expr-content pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  color: #262626;
  background: transparent;
}

.detail-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
