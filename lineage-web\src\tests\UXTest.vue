<template>
  <div class="ux-test">
    <div class="test-header">
      <h1>🎨 用户体验测试</h1>
      <p>评估界面易用性、交互流畅度和视觉效果</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-space>
        <a-button
          type="primary"
          size="large"
          :loading="isRunningTest"
          @click="runUXEvaluation"
        >
          <template #icon><ExperimentOutlined /></template>
          运行UX评估
        </a-button>

        <a-button @click="runQuickTest">快速测试</a-button>
        <a-button @click="exportReport">导出报告</a-button>
        <a-button @click="clearResults">清空结果</a-button>
      </a-space>
    </div>

    <!-- 测试结果展示 -->
    <div v-if="evaluationResult" class="test-results">
      <!-- 总体评分 -->
      <a-card title="UX评估总分" class="score-card" :bordered="false">
        <div class="score-display">
          <a-progress
            type="circle"
            :percent="evaluationResult.overall"
            :status="getScoreStatus(evaluationResult.overall)"
            :width="120"
            :stroke-color="getScoreColor(evaluationResult.overall)"
          />
          <div class="score-info">
            <h2>{{ evaluationResult.overall }}/100</h2>
            <p>{{ getScoreLabel(evaluationResult.overall) }}</p>
          </div>
        </div>
      </a-card>

      <!-- 维度评分 -->
      <a-card title="各维度评分" :bordered="false" class="dimensions-card">
        <a-row :gutter="16">
          <a-col :span="4" v-for="(score, dimension) in evaluationResult.dimensions" :key="dimension">
            <div class="dimension-item">
              <a-progress
                type="circle"
                :percent="score"
                :width="80"
                :status="getScoreStatus(score)"
                :stroke-color="getScoreColor(score)"
              />
              <h4>{{ getDimensionLabel(dimension) }}</h4>
              <p>{{ score }}/100</p>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 优势和劣势 -->
      <a-row :gutter="16" class="strengths-weaknesses">
        <a-col :span="12">
          <a-card title="优势" :bordered="false">
            <div v-if="evaluationResult.strengths.length === 0" class="empty-state">
              <a-empty description="暂无明显优势" />
            </div>
            <div v-else>
              <a-tag
                v-for="strength in evaluationResult.strengths"
                :key="strength"
                color="green"
                class="strength-tag"
              >
                <CheckCircleOutlined /> {{ strength }}
              </a-tag>
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="待改进项" :bordered="false">
            <div v-if="evaluationResult.weaknesses.length === 0" class="empty-state">
              <a-empty description="无明显问题" />
            </div>
            <div v-else>
              <a-tag
                v-for="weakness in evaluationResult.weaknesses"
                :key="weakness"
                color="orange"
                class="weakness-tag"
              >
                <ExclamationCircleOutlined /> {{ weakness }}
              </a-tag>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 详细问题列表 -->
      <a-card title="发现的问题" :bordered="false" class="issues-card">
        <div v-if="evaluationResult.issues.length === 0" class="empty-state">
          <a-result status="success" title="未发现问题" sub-title="当前页面UX表现良好" />
        </div>
        <div v-else>
          <a-collapse>
            <a-collapse-panel
              v-for="(categoryIssues, category) in groupedIssues"
              :key="category"
              :header="getDimensionLabel(String(category))"
            >
              <template #extra>
                <a-badge :count="categoryIssues.length" :number-style="{ backgroundColor: getSeverityColor(categoryIssues[0]?.severity) }" />
              </template>

              <a-list :data-source="categoryIssues" size="small">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #title>
                        <span class="issue-title">
                          <a-tag :color="getSeverityColor(item.severity)" size="small">
                            {{ getSeverityLabel(item.severity) }}
                          </a-tag>
                          {{ item.title }}
                        </span>
                      </template>
                      <template #description>
                        <div class="issue-description">
                          <p>{{ item.description }}</p>
                          <p class="suggestion"><BulbOutlined /> 建议：{{ item.suggestion }}</p>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </a-card>

      <!-- 改进建议 -->
      <a-card title="改进建议" :bordered="false" class="recommendations-card">
        <div v-if="evaluationResult.recommendations.length === 0" class="empty-state">
          <a-empty description="暂无改进建议" />
        </div>
        <div v-else>
          <a-timeline>
            <a-timeline-item
              v-for="rec in sortedRecommendations"
              :key="rec.title"
              :color="getPriorityColor(rec.priority)"
            >
              <template #dot>
                <span class="priority-dot" :style="{ backgroundColor: getPriorityColor(rec.priority) }">
                  {{ rec.priority === 'high' ? 'H' : rec.priority === 'medium' ? 'M' : 'L' }}
                </span>
              </template>

              <div class="recommendation-item">
                <h4>{{ rec.title }}</h4>
                <p>{{ rec.description }}</p>
                <div class="recommendation-meta">
                  <a-tag :color="getPriorityColor(rec.priority)" size="small">
                    {{ getPriorityLabel(rec.priority) }}优先级
                  </a-tag>
                  <a-tag color="blue" size="small">
                    {{ getEffortLabel(rec.effort) }}工作量
                  </a-tag>
                  <span class="impact">预期影响：{{ rec.impact }}</span>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-card>

      <!-- UX改进工具 -->
      <a-card title="UX改进工具" :bordered="false" class="improvement-tools">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="tool-item">
              <h4><EyeOutlined /> 视觉优化</h4>
              <p>调整颜色、字体和间距</p>
              <a-button type="primary" size="small" @click="openVisualOptimizer">
                打开工具
              </a-button>
            </div>
          </a-col>

          <a-col :span="8">
            <div class="tool-item">
              <h4><ThunderboltOutlined /> 性能优化</h4>
              <p>分析和优化加载性能</p>
              <a-button type="primary" size="small" @click="openPerformanceOptimizer">
                打开工具
              </a-button>
            </div>
          </a-col>

          <a-col :span="8">
            <div class="tool-item">
              <h4><UserOutlined /> 可访问性</h4>
              <p>提升无障碍访问体验</p>
              <a-button type="primary" size="small" @click="openAccessibilityOptimizer">
                打开工具
              </a-button>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty description="暂无评估结果，请运行UX评估" />
    </div>

    <!-- 优化工具模态框 -->
    <a-modal
      v-model:open="optimizerModalVisible"
      :title="currentOptimizerTitle"
      width="800px"
      :footer="null"
    >
      <div class="optimizer-content">
        <component :is="currentOptimizerComponent" v-if="currentOptimizerComponent" />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  ExperimentOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  BulbOutlined,
  EyeOutlined,
  ThunderboltOutlined,
  UserOutlined
} from '@ant-design/icons-vue'
import {
  uxEvaluator,
  type UXEvaluationResult,
  type UXIssue,
  type UXRecommendation,
  type UXDimensions
} from '@/utils/uxEvaluator'

// 响应式数据
const isRunningTest = ref(false)
const evaluationResult = ref<UXEvaluationResult | null>(null)
const optimizerModalVisible = ref(false)
const currentOptimizerTitle = ref('')
const currentOptimizerComponent = ref<any>(null)

// 计算属性
const groupedIssues = computed(() => {
  if (!evaluationResult.value) return {}

  const grouped: { [key: string]: UXIssue[] } = {}
  evaluationResult.value.issues.forEach(issue => {
    if (!grouped[issue.category]) {
      grouped[issue.category] = []
    }
    grouped[issue.category].push(issue)
  })

  return grouped
})

const sortedRecommendations = computed(() => {
  if (!evaluationResult.value) return []

  const priorityOrder = { high: 3, medium: 2, low: 1 }
  return [...evaluationResult.value.recommendations].sort((a, b) =>
    priorityOrder[b.priority] - priorityOrder[a.priority]
  )
})

// 运行UX评估
const runUXEvaluation = async () => {
  isRunningTest.value = true

  try {
    message.info('开始UX评估...')

    const result = await uxEvaluator.evaluateUX()
    evaluationResult.value = result

    if (result.overall >= 80) {
      message.success(`UX评估完成！总分: ${result.overall}/100`)
    } else if (result.overall >= 60) {
      message.warning(`UX评估完成，总分: ${result.overall}/100，建议优化`)
    } else {
      message.error(`UX评估完成，总分: ${result.overall}/100，需要重点改进`)
    }
  } catch (error) {
    console.error('UX评估失败:', error)
    message.error('UX评估失败')
  } finally {
    isRunningTest.value = false
  }
}

// 快速测试
const runQuickTest = async () => {
  message.info('运行快速UX测试...')

  // 模拟快速测试
  const quickResult: UXEvaluationResult = {
    overall: 85,
    dimensions: {
      usability: 88,
      accessibility: 82,
      performance: 90,
      visual: 85,
      interaction: 87,
      content: 80
    },
    issues: [
      {
        category: 'accessibility',
        severity: 'medium',
        title: '部分图片缺少alt属性',
        description: '发现3张图片没有alt属性',
        suggestion: '为所有图片添加描述性的alt属性'
      }
    ],
    recommendations: [
      {
        category: 'accessibility',
        priority: 'medium',
        title: '改善可访问性',
        description: '添加缺失的alt属性和ARIA标签',
        impact: '提升屏幕阅读器用户体验',
        effort: 'low'
      }
    ],
    strengths: ['出色的性能表现', '流畅的交互体验'],
    weaknesses: []
  }

  evaluationResult.value = quickResult
  message.success('快速测试完成！')
}

// 获取评分状态
const getScoreStatus = (score: number): 'success' | 'active' | 'exception' => {
  if (score >= 80) return 'success'
  if (score >= 60) return 'active'
  return 'exception'
}

// 获取评分颜色
const getScoreColor = (score: number): string => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#faad14'
  return '#ff4d4f'
}

// 获取评分标签
const getScoreLabel = (score: number): string => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '一般'
  if (score >= 60) return '需改进'
  return '较差'
}

// 获取维度标签
const getDimensionLabel = (dimension: string): string => {
  const labels: { [key: string]: string } = {
    usability: '易用性',
    accessibility: '可访问性',
    performance: '性能体验',
    visual: '视觉设计',
    interaction: '交互体验',
    content: '内容质量'
  }
  return labels[dimension] || dimension
}

// 获取严重程度颜色
const getSeverityColor = (severity: string): string => {
  switch (severity) {
    case 'critical': return '#ff4d4f'
    case 'high': return '#fa8c16'
    case 'medium': return '#faad14'
    case 'low': return '#52c41a'
    default: return '#d9d9d9'
  }
}

// 获取严重程度标签
const getSeverityLabel = (severity: string): string => {
  switch (severity) {
    case 'critical': return '严重'
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

// 获取优先级颜色
const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'high': return '#ff4d4f'
    case 'medium': return '#faad14'
    case 'low': return '#52c41a'
    default: return '#d9d9d9'
  }
}

// 获取优先级标签
const getPriorityLabel = (priority: string): string => {
  switch (priority) {
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

// 获取工作量标签
const getEffortLabel = (effort: string): string => {
  switch (effort) {
    case 'high': return '大'
    case 'medium': return '中'
    case 'low': return '小'
    default: return '未知'
  }
}

// 打开优化工具
const openVisualOptimizer = () => {
  currentOptimizerTitle.value = '视觉优化工具'
  currentOptimizerComponent.value = h('div', [
    h('h3', '视觉优化建议'),
    h('ul', [
      h('li', '统一颜色系统：建议使用不超过5种主色调'),
      h('li', '字体大小：确保主要文本至少14px'),
      h('li', '间距系统：使用8px基准的间距系统'),
      h('li', '对比度：确保文本与背景对比度至少4.5:1')
    ]),
    h('div', { style: 'margin-top: 16px' }, [
      h('button', {
        style: 'margin-right: 8px; padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已应用颜色优化')
      }, '应用颜色优化'),
      h('button', {
        style: 'margin-right: 8px; padding: 8px 16px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已应用字体优化')
      }, '应用字体优化'),
      h('button', {
        style: 'padding: 8px 16px; background: #faad14; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已应用间距优化')
      }, '应用间距优化')
    ])
  ])
  optimizerModalVisible.value = true
}

const openPerformanceOptimizer = () => {
  currentOptimizerTitle.value = '性能优化工具'
  currentOptimizerComponent.value = h('div', [
    h('h3', '性能优化建议'),
    h('ul', [
      h('li', '图片优化：使用WebP格式，添加懒加载'),
      h('li', '代码分割：按路由和组件进行代码分割'),
      h('li', '缓存策略：合理设置静态资源缓存'),
      h('li', '预加载：对关键资源进行预加载')
    ]),
    h('div', { style: 'margin-top: 16px' }, [
      h('button', {
        style: 'margin-right: 8px; padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已启用图片优化')
      }, '启用图片优化'),
      h('button', {
        style: 'margin-right: 8px; padding: 8px 16px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已启用代码分割')
      }, '启用代码分割'),
      h('button', {
        style: 'padding: 8px 16px; background: #faad14; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已优化缓存策略')
      }, '优化缓存策略')
    ])
  ])
  optimizerModalVisible.value = true
}

const openAccessibilityOptimizer = () => {
  currentOptimizerTitle.value = '可访问性优化工具'
  currentOptimizerComponent.value = h('div', [
    h('h3', '可访问性优化建议'),
    h('ul', [
      h('li', 'ARIA标签：为交互元素添加适当的ARIA属性'),
      h('li', '键盘导航：确保所有功能都可以通过键盘访问'),
      h('li', '焦点管理：提供清晰的焦点指示器'),
      h('li', '语义化HTML：使用正确的HTML标签结构')
    ]),
    h('div', { style: 'margin-top: 16px' }, [
      h('button', {
        style: 'margin-right: 8px; padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已添加ARIA标签')
      }, '添加ARIA标签'),
      h('button', {
        style: 'margin-right: 8px; padding: 8px 16px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已优化键盘导航')
      }, '优化键盘导航'),
      h('button', {
        style: 'padding: 8px 16px; background: #faad14; color: white; border: none; border-radius: 4px; cursor: pointer',
        onClick: () => message.success('已改善焦点管理')
      }, '改善焦点管理')
    ])
  ])
  optimizerModalVisible.value = true
}

// 导出报告
const exportReport = () => {
  if (!evaluationResult.value) {
    message.warning('请先运行UX评估')
    return
  }

  const report = {
    timestamp: new Date().toISOString(),
    evaluation: evaluationResult.value,
    metadata: {
      url: window.location.href,
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      viewport: `${window.innerWidth}x${window.innerHeight}`
    }
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `ux-evaluation-report-${new Date().toISOString().slice(0, 19)}.json`
  a.click()
  URL.revokeObjectURL(url)

  message.success('UX评估报告已导出')
}

// 清空结果
const clearResults = () => {
  evaluationResult.value = null
  message.info('评估结果已清空')
}
</script>

<style scoped>
.ux-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 28px;
}

.test-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.test-controls {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.score-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.score-card :deep(.ant-card-head-title) {
  color: white;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 24px;
}

.score-info h2 {
  margin: 0;
  color: white;
  font-size: 32px;
}

.score-info p {
  margin: 8px 0 0 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.dimensions-card {
  margin-bottom: 24px;
}

.dimension-item {
  text-align: center;
  padding: 16px;
}

.dimension-item h4 {
  margin: 12px 0 4px 0;
  color: #333;
  font-size: 14px;
}

.dimension-item p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.strengths-weaknesses {
  margin-bottom: 24px;
}

.strength-tag {
  margin: 4px;
  padding: 4px 8px;
}

.weakness-tag {
  margin: 4px;
  padding: 4px 8px;
}

.issues-card {
  margin-bottom: 24px;
}

.issue-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.issue-description {
  margin-top: 8px;
}

.suggestion {
  color: #1890ff;
  font-style: italic;
  margin-top: 4px;
}

.recommendations-card {
  margin-bottom: 24px;
}

.priority-dot {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.recommendation-item h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.recommendation-item p {
  margin: 0 0 12px 0;
  color: #666;
}

.recommendation-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.impact {
  color: #666;
  font-size: 12px;
}

.improvement-tools {
  margin-bottom: 24px;
}

.tool-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.tool-item:hover {
  background: #f0f2f5;
  border-color: #1890ff;
}

.tool-item h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.tool-item p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 12px;
}

.optimizer-content {
  padding: 16px;
}

.empty-state {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ux-test {
    padding: 16px;
  }

  .test-header {
    padding: 16px;
  }

  .test-header h1 {
    font-size: 24px;
  }

  .score-display {
    flex-direction: column;
    text-align: center;
  }

  .dimensions-card .ant-col {
    margin-bottom: 16px;
  }

  .strengths-weaknesses .ant-col {
    margin-bottom: 16px;
  }

  .improvement-tools .ant-col {
    margin-bottom: 16px;
  }
}

/* 动画效果 */
.ant-card {
  transition: all 0.3s ease;
}

.ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 进度条样式 */
.ant-progress-circle .ant-progress-text {
  font-weight: 600;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 时间轴样式 */
.ant-timeline-item-content {
  margin-left: 8px;
}

/* 折叠面板样式 */
.ant-collapse-header {
  font-weight: 500;
}

/* 列表样式 */
.ant-list-item-meta-title {
  margin-bottom: 4px;
}
</style>
