# 示例数据和演示

本文档提供了丰富的示例数据集，展示不同场景下的血缘图效果和使用方法。

## 目录

- [基础示例](#基础示例)
- [复杂查询示例](#复杂查询示例)
- [多表关联示例](#多表关联示例)
- [聚合分析示例](#聚合分析示例)
- [窗口函数示例](#窗口函数示例)
- [子查询示例](#子查询示例)
- [实际业务场景](#实际业务场景)

## 基础示例

### 简单查询

展示最基本的单表查询血缘关系。

```sql
SELECT 
  id,
  name,
  email,
  created_at
FROM users
WHERE status = 'active'
```

**血缘关系说明：**
- 源表：`users`
- 目标字段：`id`, `name`, `email`, `created_at`
- 转换类型：直接映射 (DIRECT)

### 字段别名

展示字段重命名的血缘关系。

```sql
SELECT 
  id AS user_id,
  name AS user_name,
  email AS contact_email,
  created_at AS registration_date
FROM users
```

**血缘关系说明：**
- `users.id` → `user_id` (别名转换)
- `users.name` → `user_name` (别名转换)
- `users.email` → `contact_email` (别名转换)
- `users.created_at` → `registration_date` (别名转换)

## 复杂查询示例

### 表连接查询

展示多表JOIN操作的血缘关系。

```sql
SELECT 
  u.id AS user_id,
  u.name AS user_name,
  u.email,
  o.id AS order_id,
  o.order_date,
  o.total_amount,
  p.name AS product_name,
  p.price
FROM users u
INNER JOIN orders o ON u.id = o.user_id
INNER JOIN order_items oi ON o.id = oi.order_id
INNER JOIN products p ON oi.product_id = p.id
WHERE u.status = 'active'
  AND o.order_date >= '2024-01-01'
```

**血缘关系说明：**
- 涉及表：`users`, `orders`, `order_items`, `products`
- 连接条件：
  - `users.id` = `orders.user_id`
  - `orders.id` = `order_items.order_id`
  - `order_items.product_id` = `products.id`
- 字段映射：包含直接映射和别名转换

### 聚合查询

展示GROUP BY和聚合函数的血缘关系。

```sql
SELECT 
  u.department,
  COUNT(*) AS employee_count,
  AVG(u.salary) AS avg_salary,
  MAX(u.salary) AS max_salary,
  MIN(u.hire_date) AS earliest_hire_date
FROM employees u
WHERE u.status = 'active'
GROUP BY u.department
HAVING COUNT(*) > 5
ORDER BY avg_salary DESC
```

**血缘关系说明：**
- 源表：`employees`
- 分组字段：`department` (直接映射)
- 聚合字段：
  - `COUNT(*)` → `employee_count` (聚合转换)
  - `AVG(salary)` → `avg_salary` (聚合转换)
  - `MAX(salary)` → `max_salary` (聚合转换)
  - `MIN(hire_date)` → `earliest_hire_date` (聚合转换)

## 多表关联示例

### 复杂业务查询

展示真实业务场景中的复杂查询血缘。

```sql
WITH monthly_sales AS (
  SELECT 
    DATE_TRUNC('month', o.order_date) AS month,
    p.category_id,
    SUM(oi.quantity * oi.unit_price) AS total_sales,
    COUNT(DISTINCT o.id) AS order_count,
    COUNT(DISTINCT o.user_id) AS customer_count
  FROM orders o
  JOIN order_items oi ON o.id = oi.order_id
  JOIN products p ON oi.product_id = p.id
  WHERE o.status = 'completed'
    AND o.order_date >= '2024-01-01'
  GROUP BY DATE_TRUNC('month', o.order_date), p.category_id
),
category_performance AS (
  SELECT 
    ms.month,
    c.name AS category_name,
    ms.total_sales,
    ms.order_count,
    ms.customer_count,
    LAG(ms.total_sales) OVER (
      PARTITION BY ms.category_id 
      ORDER BY ms.month
    ) AS prev_month_sales,
    ms.total_sales - LAG(ms.total_sales) OVER (
      PARTITION BY ms.category_id 
      ORDER BY ms.month
    ) AS sales_growth
  FROM monthly_sales ms
  JOIN categories c ON ms.category_id = c.id
)
SELECT 
  month,
  category_name,
  total_sales,
  order_count,
  customer_count,
  prev_month_sales,
  sales_growth,
  CASE 
    WHEN prev_month_sales IS NULL THEN 'New'
    WHEN sales_growth > 0 THEN 'Growth'
    WHEN sales_growth < 0 THEN 'Decline'
    ELSE 'Stable'
  END AS trend_status
FROM category_performance
ORDER BY month DESC, total_sales DESC
```

**血缘关系说明：**
- 涉及表：`orders`, `order_items`, `products`, `categories`
- CTE使用：`monthly_sales`, `category_performance`
- 复杂转换：
  - 日期截取：`DATE_TRUNC('month', order_date)`
  - 窗口函数：`LAG() OVER ()`
  - 条件表达式：`CASE WHEN`
  - 聚合计算：`SUM()`, `COUNT()`

## 聚合分析示例

### 销售分析报表

```sql
SELECT 
  r.region_name,
  s.store_name,
  p.category_name,
  DATE_TRUNC('quarter', o.order_date) AS quarter,
  SUM(oi.quantity * oi.unit_price) AS revenue,
  COUNT(DISTINCT o.id) AS order_count,
  COUNT(DISTINCT o.user_id) AS unique_customers,
  AVG(oi.quantity * oi.unit_price) AS avg_order_value,
  SUM(oi.quantity) AS total_quantity,
  SUM(oi.quantity * oi.unit_price) / SUM(SUM(oi.quantity * oi.unit_price)) 
    OVER (PARTITION BY r.region_name) AS revenue_share
FROM orders o
JOIN order_items oi ON o.id = oi.order_id
JOIN products p ON oi.product_id = p.id
JOIN product_categories pc ON p.category_id = pc.id
JOIN stores s ON o.store_id = s.id
JOIN regions r ON s.region_id = r.id
WHERE o.status = 'completed'
  AND o.order_date >= '2024-01-01'
GROUP BY 
  r.region_name,
  s.store_name,
  p.category_name,
  DATE_TRUNC('quarter', o.order_date)
HAVING SUM(oi.quantity * oi.unit_price) > 10000
ORDER BY 
  quarter DESC,
  revenue DESC
```

## 窗口函数示例

### 排名和趋势分析

```sql
SELECT 
  employee_id,
  employee_name,
  department,
  salary,
  hire_date,
  -- 排名函数
  ROW_NUMBER() OVER (ORDER BY salary DESC) AS salary_rank,
  RANK() OVER (PARTITION BY department ORDER BY salary DESC) AS dept_salary_rank,
  DENSE_RANK() OVER (ORDER BY hire_date) AS seniority_rank,
  
  -- 分布函数
  PERCENT_RANK() OVER (ORDER BY salary) AS salary_percentile,
  NTILE(4) OVER (ORDER BY salary) AS salary_quartile,
  
  -- 偏移函数
  LAG(salary, 1) OVER (ORDER BY hire_date) AS prev_hire_salary,
  LEAD(salary, 1) OVER (ORDER BY hire_date) AS next_hire_salary,
  FIRST_VALUE(salary) OVER (
    PARTITION BY department 
    ORDER BY hire_date 
    ROWS UNBOUNDED PRECEDING
  ) AS first_dept_salary,
  LAST_VALUE(salary) OVER (
    PARTITION BY department 
    ORDER BY hire_date 
    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
  ) AS last_dept_salary,
  
  -- 聚合函数
  SUM(salary) OVER (PARTITION BY department) AS dept_total_salary,
  AVG(salary) OVER (PARTITION BY department) AS dept_avg_salary,
  COUNT(*) OVER (PARTITION BY department) AS dept_employee_count
FROM employees
WHERE status = 'active'
ORDER BY department, hire_date
```

## 子查询示例

### 相关子查询

```sql
SELECT 
  u.id,
  u.name,
  u.email,
  u.registration_date,
  (
    SELECT COUNT(*)
    FROM orders o
    WHERE o.user_id = u.id
      AND o.status = 'completed'
  ) AS total_orders,
  (
    SELECT SUM(oi.quantity * oi.unit_price)
    FROM orders o
    JOIN order_items oi ON o.id = oi.order_id
    WHERE o.user_id = u.id
      AND o.status = 'completed'
  ) AS total_spent,
  (
    SELECT MAX(o.order_date)
    FROM orders o
    WHERE o.user_id = u.id
      AND o.status = 'completed'
  ) AS last_order_date,
  CASE 
    WHEN (
      SELECT COUNT(*)
      FROM orders o
      WHERE o.user_id = u.id
        AND o.status = 'completed'
        AND o.order_date >= CURRENT_DATE - INTERVAL '30 days'
    ) > 0 THEN 'Active'
    ELSE 'Inactive'
  END AS customer_status
FROM users u
WHERE u.status = 'active'
  AND EXISTS (
    SELECT 1
    FROM orders o
    WHERE o.user_id = u.id
      AND o.status = 'completed'
  )
ORDER BY total_spent DESC NULLS LAST
```

### 非相关子查询

```sql
SELECT 
  p.id,
  p.name,
  p.price,
  p.category_id,
  c.name AS category_name,
  p.price - (
    SELECT AVG(price)
    FROM products
    WHERE category_id = p.category_id
  ) AS price_vs_category_avg,
  CASE 
    WHEN p.price > (
      SELECT AVG(price) * 1.2
      FROM products
      WHERE category_id = p.category_id
    ) THEN 'Premium'
    WHEN p.price < (
      SELECT AVG(price) * 0.8
      FROM products
      WHERE category_id = p.category_id
    ) THEN 'Budget'
    ELSE 'Standard'
  END AS price_tier
FROM products p
JOIN categories c ON p.category_id = c.id
WHERE p.status = 'active'
  AND p.category_id IN (
    SELECT category_id
    FROM products
    GROUP BY category_id
    HAVING COUNT(*) >= 10
  )
ORDER BY c.name, p.price DESC
```

## 实际业务场景

### 电商平台用户行为分析

```sql
-- 用户购买行为分析
WITH user_purchase_behavior AS (
  SELECT
    u.id AS user_id,
    u.name AS user_name,
    u.registration_date,
    u.city,
    u.age_group,
    COUNT(DISTINCT o.id) AS total_orders,
    SUM(oi.quantity * oi.unit_price) AS total_spent,
    AVG(oi.quantity * oi.unit_price) AS avg_order_value,
    MIN(o.order_date) AS first_order_date,
    MAX(o.order_date) AS last_order_date,
    COUNT(DISTINCT p.category_id) AS categories_purchased,
    COUNT(DISTINCT DATE_TRUNC('month', o.order_date)) AS active_months
  FROM users u
  LEFT JOIN orders o ON u.id = o.user_id AND o.status = 'completed'
  LEFT JOIN order_items oi ON o.id = oi.order_id
  LEFT JOIN products p ON oi.product_id = p.id
  WHERE u.registration_date >= '2023-01-01'
  GROUP BY u.id, u.name, u.registration_date, u.city, u.age_group
),
user_segments AS (
  SELECT
    *,
    CASE
      WHEN total_orders = 0 THEN 'Never Purchased'
      WHEN total_orders = 1 THEN 'One-time Buyer'
      WHEN total_orders BETWEEN 2 AND 5 THEN 'Occasional Buyer'
      WHEN total_orders BETWEEN 6 AND 15 THEN 'Regular Customer'
      ELSE 'VIP Customer'
    END AS customer_segment,
    CASE
      WHEN last_order_date >= CURRENT_DATE - INTERVAL '30 days' THEN 'Active'
      WHEN last_order_date >= CURRENT_DATE - INTERVAL '90 days' THEN 'At Risk'
      WHEN last_order_date >= CURRENT_DATE - INTERVAL '180 days' THEN 'Dormant'
      ELSE 'Lost'
    END AS activity_status,
    NTILE(5) OVER (ORDER BY total_spent) AS spending_quintile
  FROM user_purchase_behavior
)
SELECT
  customer_segment,
  activity_status,
  city,
  age_group,
  COUNT(*) AS user_count,
  AVG(total_spent) AS avg_lifetime_value,
  AVG(avg_order_value) AS avg_order_size,
  AVG(active_months) AS avg_active_months,
  SUM(total_spent) AS segment_revenue
FROM user_segments
GROUP BY customer_segment, activity_status, city, age_group
ORDER BY segment_revenue DESC
```

### 供应链库存分析

```sql
-- 库存周转和补货分析
WITH inventory_movements AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.sku,
    c.name AS category_name,
    s.name AS supplier_name,
    w.name AS warehouse_name,

    -- 销售数据
    COALESCE(SUM(CASE WHEN im.movement_type = 'OUT' THEN im.quantity ELSE 0 END), 0) AS total_sold,
    COALESCE(SUM(CASE WHEN im.movement_type = 'IN' THEN im.quantity ELSE 0 END), 0) AS total_received,

    -- 当前库存
    COALESCE(SUM(
      CASE
        WHEN im.movement_type = 'IN' THEN im.quantity
        WHEN im.movement_type = 'OUT' THEN -im.quantity
        ELSE 0
      END
    ), 0) AS current_stock,

    -- 时间范围内的移动
    COUNT(CASE WHEN im.movement_type = 'OUT' AND im.movement_date >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) AS sales_transactions_30d,
    COALESCE(SUM(CASE WHEN im.movement_type = 'OUT' AND im.movement_date >= CURRENT_DATE - INTERVAL '30 days' THEN im.quantity ELSE 0 END), 0) AS sold_30d,
    COALESCE(SUM(CASE WHEN im.movement_type = 'OUT' AND im.movement_date >= CURRENT_DATE - INTERVAL '7 days' THEN im.quantity ELSE 0 END), 0) AS sold_7d,

    -- 成本信息
    AVG(CASE WHEN im.movement_type = 'IN' THEN im.unit_cost END) AS avg_cost,
    p.current_price,

    -- 最后移动日期
    MAX(CASE WHEN im.movement_type = 'OUT' THEN im.movement_date END) AS last_sale_date,
    MAX(CASE WHEN im.movement_type = 'IN' THEN im.movement_date END) AS last_restock_date

  FROM products p
  LEFT JOIN inventory_movements im ON p.id = im.product_id
  LEFT JOIN categories c ON p.category_id = c.id
  LEFT JOIN suppliers s ON p.supplier_id = s.id
  LEFT JOIN warehouses w ON im.warehouse_id = w.id
  WHERE im.movement_date >= CURRENT_DATE - INTERVAL '1 year'
    OR im.movement_date IS NULL
  GROUP BY p.id, p.name, p.sku, c.name, s.name, w.name, p.current_price
),
inventory_analysis AS (
  SELECT
    *,
    -- 库存周转率计算
    CASE
      WHEN current_stock > 0 AND sold_30d > 0
      THEN ROUND((sold_30d::DECIMAL / current_stock) * 30, 2)
      ELSE 0
    END AS days_of_inventory,

    -- 销售速度
    CASE
      WHEN sold_30d > 0
      THEN ROUND(sold_30d::DECIMAL / 30, 2)
      ELSE 0
    END AS daily_sales_rate,

    -- 库存状态
    CASE
      WHEN current_stock <= 0 THEN 'Out of Stock'
      WHEN current_stock <= sold_7d THEN 'Critical Low'
      WHEN current_stock <= sold_30d * 0.5 THEN 'Low Stock'
      WHEN current_stock >= sold_30d * 3 THEN 'Overstock'
      ELSE 'Normal'
    END AS stock_status,

    -- 利润率
    CASE
      WHEN avg_cost > 0
      THEN ROUND(((current_price - avg_cost) / avg_cost) * 100, 2)
      ELSE 0
    END AS profit_margin_pct
  FROM inventory_movements
)
SELECT
  category_name,
  supplier_name,
  warehouse_name,
  stock_status,
  COUNT(*) AS product_count,
  SUM(current_stock) AS total_stock_units,
  SUM(current_stock * current_price) AS total_stock_value,
  AVG(days_of_inventory) AS avg_days_of_inventory,
  AVG(daily_sales_rate) AS avg_daily_sales_rate,
  AVG(profit_margin_pct) AS avg_profit_margin,
  SUM(sold_30d) AS total_sold_30d,
  SUM(sold_30d * current_price) AS total_revenue_30d
FROM inventory_analysis
GROUP BY category_name, supplier_name, warehouse_name, stock_status
ORDER BY total_stock_value DESC
```

### 金融风险评估

```sql
-- 客户信用风险评估
WITH customer_financial_profile AS (
  SELECT
    c.customer_id,
    c.customer_name,
    c.customer_type,
    c.industry,
    c.registration_date,
    c.credit_limit,

    -- 交易历史
    COUNT(t.transaction_id) AS total_transactions,
    SUM(CASE WHEN t.transaction_type = 'CREDIT' THEN t.amount ELSE 0 END) AS total_credits,
    SUM(CASE WHEN t.transaction_type = 'DEBIT' THEN t.amount ELSE 0 END) AS total_debits,
    SUM(t.amount) AS net_balance,

    -- 最近活动
    COUNT(CASE WHEN t.transaction_date >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) AS transactions_30d,
    SUM(CASE WHEN t.transaction_date >= CURRENT_DATE - INTERVAL '30 days' THEN ABS(t.amount) ELSE 0 END) AS volume_30d,

    -- 逾期情况
    COUNT(CASE WHEN t.transaction_type = 'DEBIT' AND t.due_date < t.transaction_date THEN 1 END) AS overdue_count,
    SUM(CASE WHEN t.transaction_type = 'DEBIT' AND t.due_date < t.transaction_date THEN t.amount ELSE 0 END) AS overdue_amount,

    -- 时间分析
    MAX(t.transaction_date) AS last_transaction_date,
    AVG(CASE WHEN t.transaction_type = 'DEBIT' THEN t.transaction_date - t.due_date ELSE NULL END) AS avg_payment_delay,

    -- 账户余额历史
    AVG(ab.balance) AS avg_balance,
    MIN(ab.balance) AS min_balance,
    MAX(ab.balance) AS max_balance,
    STDDEV(ab.balance) AS balance_volatility

  FROM customers c
  LEFT JOIN transactions t ON c.customer_id = t.customer_id
  LEFT JOIN account_balances ab ON c.customer_id = ab.customer_id
  WHERE c.status = 'ACTIVE'
    AND (t.transaction_date >= CURRENT_DATE - INTERVAL '2 years' OR t.transaction_date IS NULL)
    AND (ab.balance_date >= CURRENT_DATE - INTERVAL '1 year' OR ab.balance_date IS NULL)
  GROUP BY c.customer_id, c.customer_name, c.customer_type, c.industry, c.registration_date, c.credit_limit
),
risk_scoring AS (
  SELECT
    *,
    -- 风险评分计算
    CASE
      WHEN overdue_count = 0 THEN 100
      WHEN overdue_count <= 2 THEN 80
      WHEN overdue_count <= 5 THEN 60
      WHEN overdue_count <= 10 THEN 40
      ELSE 20
    END AS payment_score,

    CASE
      WHEN transactions_30d >= 10 THEN 100
      WHEN transactions_30d >= 5 THEN 80
      WHEN transactions_30d >= 2 THEN 60
      WHEN transactions_30d >= 1 THEN 40
      ELSE 20
    END AS activity_score,

    CASE
      WHEN net_balance >= credit_limit * 0.8 THEN 100
      WHEN net_balance >= credit_limit * 0.5 THEN 80
      WHEN net_balance >= 0 THEN 60
      WHEN net_balance >= credit_limit * -0.5 THEN 40
      ELSE 20
    END AS balance_score,

    CASE
      WHEN balance_volatility <= avg_balance * 0.1 THEN 100
      WHEN balance_volatility <= avg_balance * 0.3 THEN 80
      WHEN balance_volatility <= avg_balance * 0.5 THEN 60
      WHEN balance_volatility <= avg_balance * 0.8 THEN 40
      ELSE 20
    END AS stability_score
  FROM customer_financial_profile
),
final_risk_assessment AS (
  SELECT
    *,
    ROUND((payment_score * 0.4 + activity_score * 0.2 + balance_score * 0.3 + stability_score * 0.1), 2) AS overall_risk_score,
    CASE
      WHEN (payment_score * 0.4 + activity_score * 0.2 + balance_score * 0.3 + stability_score * 0.1) >= 80 THEN 'Low Risk'
      WHEN (payment_score * 0.4 + activity_score * 0.2 + balance_score * 0.3 + stability_score * 0.1) >= 60 THEN 'Medium Risk'
      WHEN (payment_score * 0.4 + activity_score * 0.2 + balance_score * 0.3 + stability_score * 0.1) >= 40 THEN 'High Risk'
      ELSE 'Very High Risk'
    END AS risk_category
  FROM risk_scoring
)
SELECT
  industry,
  customer_type,
  risk_category,
  COUNT(*) AS customer_count,
  AVG(overall_risk_score) AS avg_risk_score,
  SUM(credit_limit) AS total_credit_exposure,
  SUM(net_balance) AS total_net_balance,
  SUM(overdue_amount) AS total_overdue,
  AVG(volume_30d) AS avg_monthly_volume,
  ROUND(SUM(overdue_amount)::DECIMAL / NULLIF(SUM(credit_limit), 0) * 100, 2) AS overdue_ratio_pct
FROM final_risk_assessment
GROUP BY industry, customer_type, risk_category
ORDER BY total_credit_exposure DESC, avg_risk_score ASC
```

## 演示数据生成

为了便于测试和演示，我们提供了数据生成脚本：

### 基础数据结构

```typescript
// 示例数据生成器
export const generateSampleData = (scenario: string) => {
  switch (scenario) {
    case 'ecommerce':
      return generateEcommerceData()
    case 'finance':
      return generateFinanceData()
    case 'supply-chain':
      return generateSupplyChainData()
    case 'analytics':
      return generateAnalyticsData()
    default:
      return generateBasicData()
  }
}

// 电商场景数据
const generateEcommerceData = (): LineageData => {
  return {
    tables: {
      users: {
        name: 'users',
        type: 'table',
        description: '用户表',
        fields: [
          { id: 'users.id', fieldName: 'id', tableName: 'users', type: 'field', dataType: { type: 'INT', isPrimaryKey: true } },
          { id: 'users.name', fieldName: 'name', tableName: 'users', type: 'field', dataType: { type: 'VARCHAR', length: 100 } },
          { id: 'users.email', fieldName: 'email', tableName: 'users', type: 'field', dataType: { type: 'VARCHAR', length: 255 } },
          { id: 'users.registration_date', fieldName: 'registration_date', tableName: 'users', type: 'field', dataType: { type: 'TIMESTAMP' } }
        ]
      },
      orders: {
        name: 'orders',
        type: 'table',
        description: '订单表',
        fields: [
          { id: 'orders.id', fieldName: 'id', tableName: 'orders', type: 'field', dataType: { type: 'INT', isPrimaryKey: true } },
          { id: 'orders.user_id', fieldName: 'user_id', tableName: 'orders', type: 'field', dataType: { type: 'INT', isForeignKey: true } },
          { id: 'orders.order_date', fieldName: 'order_date', tableName: 'orders', type: 'field', dataType: { type: 'TIMESTAMP' } },
          { id: 'orders.total_amount', fieldName: 'total_amount', tableName: 'orders', type: 'field', dataType: { type: 'DECIMAL', precision: 10, scale: 2 } }
        ]
      }
    },
    nodes: [
      // 用户表字段
      { id: 'users.id', fieldName: 'id', tableName: 'users', type: 'field', dataType: { type: 'INT', isPrimaryKey: true }, label: 'id' },
      { id: 'users.name', fieldName: 'name', tableName: 'users', type: 'field', dataType: { type: 'VARCHAR', length: 100 }, label: 'name' },
      // 订单表字段
      { id: 'orders.id', fieldName: 'id', tableName: 'orders', type: 'field', dataType: { type: 'INT', isPrimaryKey: true }, label: 'id' },
      { id: 'orders.user_id', fieldName: 'user_id', tableName: 'orders', type: 'field', dataType: { type: 'INT', isForeignKey: true }, label: 'user_id' }
    ],
    edges: [
      {
        id: 'edge_1',
        source: 'users.id',
        target: 'orders.user_id',
        transformType: 'JOIN',
        label: '用户关联',
        confidence: 1.0
      }
    ],
    metadata: {
      version: '1.0.0',
      parseTime: new Date().toISOString(),
      sqlText: 'SELECT u.id, u.name, o.total_amount FROM users u JOIN orders o ON u.id = o.user_id'
    }
  }
}
```

这些示例展示了从简单查询到复杂业务场景的各种血缘关系，帮助用户理解和使用血缘图组件。
