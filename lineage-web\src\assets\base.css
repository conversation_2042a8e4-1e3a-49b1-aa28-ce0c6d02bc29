/* Modern flat design color palette with soft blue and orange accents */
:root {
  /* Base colors - Pure white background */
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #fafafa;
  --vt-c-white-mute: #f8f9fa;

  --vt-c-black: #000000;
  --vt-c-black-soft: #141414;
  --vt-c-black-mute: #1f1f1f;

  /* Primary colors - Soft blue palette */
  --vt-c-primary: #4A90E2;
  --vt-c-primary-light: #6BA3E8;
  --vt-c-primary-dark: #357ABD;
  --vt-c-primary-lighter: #E8F2FF;
  --vt-c-primary-subtle: #F0F7FF;

  /* Secondary colors - Warm orange palette */
  --vt-c-secondary: #FF8A50;
  --vt-c-secondary-light: #FFB380;
  --vt-c-secondary-dark: #E6743D;
  --vt-c-secondary-lighter: #FFF2ED;
  --vt-c-secondary-subtle: #FFF8F5;

  /* Neutral colors - Softer gray palette */
  --vt-c-gray-1: #f8f9fa;
  --vt-c-gray-2: #f1f3f4;
  --vt-c-gray-3: #e8eaed;
  --vt-c-gray-4: #dadce0;
  --vt-c-gray-5: #9aa0a6;
  --vt-c-gray-6: #5f6368;
  --vt-c-gray-7: #3c4043;
  --vt-c-gray-8: #202124;
  --vt-c-gray-9: #1a1a1a;
  --vt-c-gray-10: #121212;

  /* Status colors - Harmonized with blue/orange theme */
  --vt-c-success: #34A853;
  --vt-c-warning: #FBBC04;
  --vt-c-error: #EA4335;
  --vt-c-info: #4A90E2;

  /* Accent colors for highlights and emphasis */
  --vt-c-accent-blue: #4A90E2;
  --vt-c-accent-orange: #FF8A50;
  --vt-c-accent-green: #34A853;
  --vt-c-accent-purple: #9C27B0;

  /* Divider colors */
  --vt-c-divider-light-1: rgba(0, 0, 0, 0.06);
  --vt-c-divider-light-2: rgba(0, 0, 0, 0.04);
  --vt-c-divider-dark-1: rgba(255, 255, 255, 0.12);
  --vt-c-divider-dark-2: rgba(255, 255, 255, 0.08);

  /* Text colors */
  --vt-c-text-light-1: var(--vt-c-gray-8);
  --vt-c-text-light-2: var(--vt-c-gray-6);
  --vt-c-text-light-3: var(--vt-c-gray-5);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(255, 255, 255, 0.85);
  --vt-c-text-dark-3: rgba(255, 255, 255, 0.65);
}

/* Semantic color variables for modern clean design */
:root {
  /* Background colors - Pure white base */
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);
  --color-background-panel: var(--vt-c-gray-1);
  --color-background-elevated: var(--vt-c-white);

  /* Border colors - Subtle and clean */
  --color-border: var(--vt-c-gray-3);
  --color-border-light: var(--vt-c-gray-2);
  --color-border-hover: var(--vt-c-gray-4);
  --color-border-focus: var(--vt-c-primary);

  /* Text colors - Clear hierarchy */
  --color-heading: var(--vt-c-gray-8);
  --color-text: var(--vt-c-gray-7);
  --color-text-secondary: var(--vt-c-gray-6);
  --color-text-tertiary: var(--vt-c-gray-5);
  --color-text-disabled: var(--vt-c-gray-4);

  /* Primary theme colors - Soft blue */
  --color-primary: var(--vt-c-primary);
  --color-primary-light: var(--vt-c-primary-light);
  --color-primary-dark: var(--vt-c-primary-dark);
  --color-primary-lighter: var(--vt-c-primary-lighter);
  --color-primary-subtle: var(--vt-c-primary-subtle);

  /* Secondary theme colors - Warm orange */
  --color-secondary: var(--vt-c-secondary);
  --color-secondary-light: var(--vt-c-secondary-light);
  --color-secondary-dark: var(--vt-c-secondary-dark);
  --color-secondary-lighter: var(--vt-c-secondary-lighter);
  --color-secondary-subtle: var(--vt-c-secondary-subtle);

  /* Surface colors */
  --color-surface: var(--vt-c-white);
  --color-surface-variant: var(--vt-c-gray-1);
  --color-surface-container: var(--vt-c-gray-2);

  /* Shadow colors - Subtle and clean */
  --color-shadow-light: rgba(0, 0, 0, 0.02);
  --color-shadow-medium: rgba(0, 0, 0, 0.04);
  --color-shadow-heavy: rgba(0, 0, 0, 0.08);
  --color-shadow-elevated: rgba(0, 0, 0, 0.12);

  /* Border radius - Modern and consistent */
  --border-radius-xs: 2px;
  --border-radius-small: 4px;
  --border-radius-medium: 6px;
  --border-radius-large: 8px;
  --border-radius-xlarge: 12px;
  --border-radius-xxlarge: 16px;

  /* Spacing system - Consistent scale */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
  --spacing-xxxl: 32px;

  /* Layout spacing */
  --section-gap: 24px;
  --panel-padding: 20px;
  --content-padding: 16px;

  /* Split layout proportions */
  --left-panel-width: 40%;
  --right-panel-width: 60%;
  --panel-min-width: 300px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black-soft);
    --color-background-soft: var(--vt-c-black-mute);
    --color-background-mute: var(--vt-c-gray-9);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-1);
    --color-text-secondary: var(--vt-c-text-dark-2);
    --color-text-tertiary: var(--vt-c-text-dark-3);

    /* Surface colors for dark theme */
    --color-surface: var(--vt-c-black-mute);
    --color-surface-variant: var(--vt-c-gray-9);

    /* Shadow colors for dark theme */
    --color-shadow-light: rgba(0, 0, 0, 0.2);
    --color-shadow-medium: rgba(0, 0, 0, 0.3);
    --color-shadow-heavy: rgba(0, 0, 0, 0.4);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1.5;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    'Noto Sans',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji';
  font-size: 14px;
  font-weight: 400;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
